from pydantic import BaseModel
from typing import List, Dict, Optional
from enum import Enum

class Stage(str, Enum):
    """对话阶段枚举"""
    OPENING = "开场白"
    CHAT = "话天地"
    INTRODUCTION = "人选介绍"
    MAIN_TOPIC = "入主题"
    CLOSING = "促成"

class CustomerProfile(BaseModel):
    """客户画像模型"""
    婚姻状态: str = "单身"
    婚姻意愿: str = "想结婚"
    年龄: str = "25-30岁"
    性别: str = "女"
    学历: str = "本科"
    收入区间: str = "8000-15000"
    地理位置: str = "北京"
    对婚恋公司的疑虑: str = "担心被骗钱"
    对象诉求: str = "希望找到有稳定工作、性格温和的对象"

class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str  # "user" 或 "assistant"
    content: str
    timestamp: Optional[str] = None

class SessionData(BaseModel):
    """会话数据模型"""
    session_id: str
    customer_profile: CustomerProfile
    current_stage: Stage
    chat_history: List[ChatMessage] = []
    stage_scores: Dict[str, Dict[str, float]] = {}
    stage_attempts: Dict[str, int] = {}
    is_stage_passed: Dict[str, bool] = {}

class EvaluationCriteria(BaseModel):
    """评分标准模型"""
    自我介绍明确性: float = 0.0
    来电目的说明: float = 0.0
    获号渠道说明: float = 0.0
    匹配点提及: float = 0.0
    避免强推话术: float = 0.0
    语气自然度: float = 0.0

class EvaluationResult(BaseModel):
    """评分结果模型"""
    criteria_scores: EvaluationCriteria
    average_score: float
    passed: bool
    feedback: str
    suggestions: List[str] = []

class ChatRequest(BaseModel):
    """聊天请求模型"""
    session_id: str
    message: str
    stage: Stage

class ChatResponse(BaseModel):
    """聊天响应模型"""
    response: str
    evaluation: Optional[EvaluationResult] = None
    stage_completed: bool = False
    next_stage: Optional[Stage] = None
