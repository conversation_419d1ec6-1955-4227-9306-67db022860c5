// 全局变量
let currentSessionId = null;
let currentStage = '开场白';
let isWaitingForResponse = false;
let evaluationHistory = []; // 存储历史评分记录
let conversationRounds = 0; // 对话轮次计数
let chatStageScores = { // 话天地阶段评分
    '需求挖掘深度': 0,
    '共情能力展现': 0,
    '疑虑处理效果': 0,
    '专业度体现': 0,
    '对话节奏把控': 0,
    '信任关系建立': 0,
    '扣题程度': 0
};

let candidateIntroScores = { // 人选介绍阶段评分
    '匹配精准度': 0,
    '信息具体性': 0,
    '多样性展示': 0,
    '生活化描述': 0,
    '互动性保持': 0,
    '真实感营造': 0,
    '扣题程度': 0
};

// DOM元素
const configPanel = document.getElementById('config-panel');
const chatPanel = document.getElementById('chat-panel');
const evaluationPanel = document.getElementById('evaluation-panel');
const customerForm = document.getElementById('customer-form');
const chatMessages = document.getElementById('chat-messages');
const chatInput = document.getElementById('chat-input');
const sendBtn = document.getElementById('send-btn');
const currentStageSpan = document.getElementById('current-stage');
const continueBtn = document.getElementById('continue-btn');
const retryBtn = document.getElementById('retry-btn');
const toggleHistoryBtn = document.getElementById('toggle-history-btn');
const historyPanel = document.getElementById('history-panel');
const historyContent = document.getElementById('history-content');
const realtimeScoreCard = document.getElementById('realtime-score-card');
const conversationRoundsSpan = document.getElementById('conversation-rounds');
const averageScoreSpan = document.getElementById('average-score');
const progressFill = document.getElementById('progress-fill');
const feedbackContent = document.getElementById('feedback-content');
const roundScoreChanges = document.getElementById('round-score-changes');
const scoreChangesContent = document.getElementById('score-changes-content');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    
    // 添加输入框状态监控器，用于调试
    setInterval(() => {
        const userInput = document.getElementById('chat-input');
        if (userInput && currentStage === '人选介绍' && userInput.disabled) {
            console.warn('检测到人选介绍阶段输入框被意外禁用，尝试重新启用');
            userInput.disabled = false;
            userInput.removeAttribute('disabled');
        }
    }, 1000);
});

function setupEventListeners() {
    // 表单提交
    customerForm.addEventListener('submit', handleFormSubmit);

    // 发送消息
    sendBtn.addEventListener('click', sendMessage);
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // 按钮事件
    continueBtn.addEventListener('click', continueToNextStage);
    retryBtn.addEventListener('click', retryCurrentStage);
    toggleHistoryBtn.addEventListener('click', toggleHistoryPanel);
}

// 强制启用输入框的专用函数
function forceEnableInputBox() {
    console.log('=== 强制启用输入框 ===');
    const userInput = document.getElementById('chat-input');
    const sendButton = document.getElementById('send-btn');
    
    if (userInput) {
        console.log('启用前状态:', {
            disabled: userInput.disabled,
            hasDisabledAttr: userInput.hasAttribute('disabled'),
            readOnly: userInput.readOnly,
            style: userInput.style.cssText
        });
        
        // 多种方式强制启用
        userInput.disabled = false;
        userInput.removeAttribute('disabled');
        userInput.removeAttribute('readonly');
        userInput.readOnly = false;
        
        // 清除样式限制
        userInput.style.pointerEvents = 'auto';
        userInput.style.opacity = '1';
        userInput.style.cursor = 'text';
        userInput.style.backgroundColor = '';
        userInput.style.color = '';
        
        // 重新设置基本属性
        userInput.placeholder = '请输入您的话术...';
        
        console.log('启用后状态:', {
            disabled: userInput.disabled,
            hasDisabledAttr: userInput.hasAttribute('disabled'),
            readOnly: userInput.readOnly,
            style: userInput.style.cssText
        });
        
        // 测试输入功能
        const testValue = 'test_' + Date.now();
        userInput.value = testValue;
        const canInput = userInput.value === testValue;
        userInput.value = '';
        console.log('输入框功能测试:', canInput ? '正常' : '异常');
        
        userInput.focus();
    }
    
    if (sendButton) {
        sendButton.disabled = false;
        sendButton.removeAttribute('disabled');
        sendButton.style.pointerEvents = 'auto';
        sendButton.style.opacity = '1';
        sendButton.style.cursor = 'pointer';
    }
    
    // 重置全局状态
    isWaitingForResponse = false;
    
    console.log('=== 输入框启用完成 ===');
}

async function handleFormSubmit(e) {
    e.preventDefault();

    const formData = new FormData(customerForm);
    const customerProfile = {};

    for (let [key, value] of formData.entries()) {
        customerProfile[key] = value;
    }

    try {
        const response = await fetch('/api/start-session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(customerProfile)
        });

        if (!response.ok) {
            throw new Error('Failed to start session');
        }

        const data = await response.json();
        currentSessionId = data.session_id;
        currentStage = data.current_stage;

        // 切换到聊天界面
        configPanel.style.display = 'none';
        chatPanel.style.display = 'block';

        // 添加系统消息
        addMessage('system', data.message);

        // 更新阶段显示
        updateStageDisplay();

    } catch (error) {
        alert('启动会话失败: ' + error.message);
    }
}

async function sendMessage() {
    if (isWaitingForResponse || !chatInput.value.trim()) {
        return;
    }

    const message = chatInput.value.trim();
    chatInput.value = '';
    isWaitingForResponse = true;
    sendBtn.disabled = true;
    sendBtn.innerHTML = '<span class="loading"></span> 发送中...';

    // 添加用户消息
    addMessage('user', message);

    try {
        if (currentStage === '开场白') {
            await handleOpeningStage(message);
        } else {
            await handleConversationStage(message);
        }
    } catch (error) {
        addMessage('system', '发送失败: ' + error.message);
        // 发送失败时恢复按钮状态
        isWaitingForResponse = false;
        sendBtn.disabled = false;
        sendBtn.textContent = '发送';
    }
}

async function handleOpeningStage(message) {
    const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_id: currentSessionId,
            message: message,
            stage: currentStage
        })
    });

    if (!response.ok) {
        throw new Error('Request failed');
    }

    // 消息发送成功，立即恢复按钮状态
    isWaitingForResponse = false;
    sendBtn.disabled = false;
    sendBtn.textContent = '发送';

    // 处理流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let customerMessage = '';
    let customerMessageElement = null;
    let evaluationStarted = false;

    while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
            if (line.startsWith('data: ')) {
                try {
                    const data = JSON.parse(line.slice(6));

                    if (data.type === 'customer_response') {
                        // 流式显示客户回复
                        if (!customerMessageElement) {
                            customerMessageElement = addMessage('assistant', '');
                        }
                        customerMessage += data.data;
                        customerMessageElement.textContent = customerMessage;
                        scrollToBottom();
                    } else if (data.type === 'customer_complete') {
                        // 客户回复完成
                        scrollToBottom();
                    } else if (data.type === 'evaluation_start') {
                        // 开始评分，显示加载状态
                        if (!evaluationStarted) {
                            evaluationStarted = true;
                            showEvaluationLoading();
                        }
                    } else if (data.type === 'evaluation_complete') {
                        // 评分完成，显示结果
                        hideEvaluationLoading();
                        showEvaluationResult(data);
                        
                        // 不立即更新阶段显示，等待用户手动点击进入下一阶段
                        // 保存下一阶段信息，但不更新UI
                        if (data.stage_completed) {
                            // 保存下一阶段信息，但不立即切换
                            window.nextStage = data.next_stage;
                        }
                    }
                } catch (e) {
                    // 忽略解析错误
                }
            }
        }
    }
}

async function handleConversationStage(message) {
    // 如果是话天地或人选介绍阶段，增加对话轮次
    if (currentStage === '话天地' || currentStage === '人选介绍') {
        conversationRounds++;
        updateConversationRounds();
    }

    const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_id: currentSessionId,
            message: message,
            stage: currentStage
        })
    });

    if (!response.ok) {
        throw new Error('Request failed');
    }

    // 消息发送成功，立即恢复按钮状态
    isWaitingForResponse = false;
    sendBtn.disabled = false;
    sendBtn.textContent = '发送';

    // 处理流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let assistantMessage = '';
    let messageElement = null;

    while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
            if (line.startsWith('data: ')) {
                try {
                    const data = JSON.parse(line.slice(6));

                    if (data.type === 'content') {
                        if (!messageElement) {
                            messageElement = addMessage('assistant', '');
                        }
                        assistantMessage += data.data;
                        messageElement.textContent = assistantMessage;
                        scrollToBottom();
                    } else if (data.type === 'complete') {
                        // 流式输出完成
                        break;
                    }
                } catch (e) {
                    // 忽略解析错误
                }
            }
        }
    }

    // 如果是话天地阶段，在对话完成后进行评分
    if (currentStage === '话天地') {
        await evaluateChatStageMessage(message);
    } else if (currentStage === '人选介绍') {
        await evaluateCandidateIntroMessage(message);
    }
}

function addMessage(role, content) {
    const messageContainer = document.createElement('div');
    messageContainer.className = `message-container ${role}`;
    
    // 创建头像和名字
    if (role === 'user' || role === 'assistant') {
        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        
        const avatar = document.createElement('div');
        avatar.className = 'avatar';
        avatar.textContent = role === 'user' ? '👤' : '🤖';
        
        const name = document.createElement('div');
        name.className = 'speaker-name';
        name.textContent = role === 'user' ? '红娘' : '客户';
        
        avatarDiv.appendChild(avatar);
        avatarDiv.appendChild(name);
        messageContainer.appendChild(avatarDiv);
    }
    
    // 创建消息内容
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}`;
    messageDiv.textContent = content;
    messageContainer.appendChild(messageDiv);
    
    chatMessages.appendChild(messageContainer);
    scrollToBottom();
    return messageDiv;
}

function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function showEvaluationResult(data) {
    const evaluation = data.evaluation;

    // 保存评分结果到历史记录
    const historyItem = {
        stage: currentStage,
        evaluation: evaluation,
        timestamp: new Date().toLocaleString('zh-CN')
    };
    evaluationHistory.push(historyItem);

    let html = '<div class="evaluation-content">';

    // 显示各项评分
    html += '<h4>评分详情:</h4>';
    for (const [criteria, score] of Object.entries(evaluation.criteria_scores)) {
        const scoreClass = score >= 80 ? 'good' : score >= 60 ? 'average' : 'poor';
        html += `
            <div class="score-item">
                <span>${criteria}</span>
                <span class="score-value ${scoreClass}">${score}/100</span>
            </div>
        `;
    }

    // 显示平均分
    const avgClass = evaluation.average_score >= 80 ? 'good' : evaluation.average_score >= 60 ? 'average' : 'poor';
    html += `
        <div class="average-score">
            平均分: ${evaluation.average_score}/100
            ${evaluation.passed ? '✅ 通过' : '❌ 未通过'}
        </div>
    `;

    // 显示改进建议
    if (evaluation.suggestions && evaluation.suggestions.length > 0) {
        html += '<div class="suggestions">';
        html += '<h4>改进建议:</h4>';
        html += '<ul>';
        for (const suggestion of evaluation.suggestions) {
            html += `<li>${suggestion}</li>`;
        }
        html += '</ul>';
        html += '</div>';
    }

    // 显示总体评价
    if (evaluation.feedback) {
        html += `<div class="feedback"><h4>总体评价:</h4><p>${evaluation.feedback}</p></div>`;
    }

    html += '</div>';

    document.getElementById('evaluation-content').innerHTML = html;

    // 显示按钮
    if (data.stage_completed) {
        // 开场白通过后显示继续按钮，需要手动点击进入下一阶段
        continueBtn.style.display = 'inline-block';
        continueBtn.textContent = '进入下一阶段';
        addMessage('system', `🎉 ${data.message}`);

        // 显示历史记录按钮
        toggleHistoryBtn.style.display = 'inline-block';
        updateHistoryPanel();

        // 不自动显示实时评分卡片，等待用户点击进入下一阶段
        // 不自动隐藏评分面板，让用户手动点击进入下一阶段
    } else {
        continueBtn.style.display = 'none';
        addMessage('system', `⚠️ ${data.message}`);
    }

    // 显示评分面板
    evaluationPanel.style.display = 'block';
    evaluationPanel.scrollIntoView({ behavior: 'smooth' });
}

function updateStageDisplay() {
    currentStageSpan.textContent = currentStage;

    // 更新进度条
    const stageItems = document.querySelectorAll('.stage-item');
    stageItems.forEach(item => {
        item.classList.remove('active', 'completed');
        if (item.dataset.stage === currentStage) {
            item.classList.add('active');
        }
    });
}

async function continueToNextStage() {
    try {
        // 如果有保存的下一阶段信息，直接使用
        if (window.nextStage) {
            currentStage = window.nextStage;
            window.nextStage = null; // 清除保存的信息
            
            // 隐藏评分面板
            evaluationPanel.style.display = 'none';

            // 更新阶段显示
            updateStageDisplay();

            // 如果进入话天地阶段，显示实时评分卡片
            if (currentStage === '话天地') {
                console.log('进入话天地阶段，显示实时评分卡片'); // 调试日志
                realtimeScoreCard.style.display = 'flex';
                realtimeScoreCard.style.flexDirection = 'column';
                realtimeScoreCard.style.visibility = 'visible';
                realtimeScoreCard.style.opacity = '1';
                initializeChatStageScoring();
                addMessage('system', '现在可以开始与客户对话了，请根据客户的回应调整您的话术策略。右侧显示实时评分情况。');
            } else if (currentStage === '人选介绍') {
                console.log('进入人选介绍阶段，显示实时评分卡片'); // 调试日志
                realtimeScoreCard.style.display = 'flex';
                realtimeScoreCard.style.flexDirection = 'column';
                realtimeScoreCard.style.visibility = 'visible';
                realtimeScoreCard.style.opacity = '1';
                
                // 初始化人选介绍阶段评分
                initializeCandidateIntroScoring();
                
                addMessage('system', '现在开始人选介绍阶段，请向客户介绍合适的候选人。右侧显示实时评分情况。');
                
                // 立即强制启用输入框
                forceEnableInputBox();
                
                // 延迟再次确保启用
                setTimeout(() => {
                    forceEnableInputBox();
                }, 200);
                
                // 再次延迟确保
                setTimeout(() => {
                    forceEnableInputBox();
                }, 500);
            }

            // 添加系统消息
            addMessage('system', `已进入阶段：${currentStage}`);
        } else {
            // 否则调用后端API
            const response = await fetch('/api/next-stage', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: currentSessionId
                })
            });

            if (!response.ok) {
                throw new Error('Failed to continue to next stage');
            }

            const data = await response.json();
            currentStage = data.current_stage;

            // 隐藏评分面板
            evaluationPanel.style.display = 'none';

            // 更新阶段显示
            updateStageDisplay();

            // 添加系统消息
            addMessage('system', data.message);
        }

    } catch (error) {
        alert('进入下一阶段失败: ' + error.message);
    }
}

function retryCurrentStage() {
    // 隐藏评分面板
    evaluationPanel.style.display = 'none';

    if (currentStage === '话天地') {
        // 话天地阶段的重试逻辑
        restartChatStage();
    } else if (currentStage === '人选介绍') {
        // 人选介绍阶段的重试逻辑
        restartCandidateIntroStage();
    } else {
        // 其他阶段的重试逻辑
        addMessage('system', '请重新开始当前阶段的话术练习');
        chatInput.focus();
    }
}

function toggleHistoryPanel() {
    if (historyPanel.style.display === 'none') {
        historyPanel.style.display = 'block';
        toggleHistoryBtn.textContent = '📊 隐藏历史评分记录';
        historyPanel.scrollIntoView({ behavior: 'smooth' });
    } else {
        historyPanel.style.display = 'none';
        toggleHistoryBtn.textContent = '📊 查看历史评分记录';
    }
}

function updateHistoryPanel() {
    if (evaluationHistory.length === 0) {
        historyContent.innerHTML = '<p>暂无历史评分记录</p>';
        return;
    }

    let html = '';
    for (const item of evaluationHistory) {
        html += `
            <div class="history-item">
                <div class="history-stage">${item.stage} - ${item.timestamp}</div>
                
                <div class="history-scores">
        `;
        
        // 显示各项评分
        for (const [criteria, score] of Object.entries(item.evaluation.criteria_scores)) {
            const scoreClass = score >= 80 ? 'good' : score >= 60 ? 'average' : 'poor';
            html += `
                <div class="history-score-item">
                    <span>${criteria}</span>
                    <span class="score-value ${scoreClass}">${score}/100</span>
                </div>
            `;
        }
        
        html += '</div>';
        
        // 显示平均分
        const avgClass = item.evaluation.average_score >= 80 ? 'good' : item.evaluation.average_score >= 60 ? 'average' : 'poor';
        html += `
            <div class="history-average">
                平均分: ${item.evaluation.average_score}/100
                ${item.evaluation.passed ? '✅ 通过' : '❌ 未通过'}
            </div>
        `;
        
        // 显示改进建议
        if (item.evaluation.suggestions && item.evaluation.suggestions.length > 0) {
            html += `
                <div class="history-suggestions">
                    <h5>改进建议:</h5>
                    <ul>
            `;
            for (const suggestion of item.evaluation.suggestions) {
                html += `<li>${suggestion}</li>`;
            }
            html += `
                    </ul>
                </div>
            `;
        }
        
        html += '</div>';
    }
    
    historyContent.innerHTML = html;
}

function initializeChatStageScoring() {
    console.log('初始化话天地阶段评分');
    
    // 重置评分数据
    conversationRounds = 0;
    chatStageScores = {
        '需求挖掘深度': 0,
        '共情能力展现': 0,
        '疑虑处理效果': 0,
        '专业度体现': 0,
        '对话节奏把控': 0,
        '信任关系建立': 0,
        '扣题程度': 0
    };
    
    // 更新评分卡片标题（在切换显示之前）
    const scoreCardTitle = document.getElementById('score-card-title');
    if (scoreCardTitle) {
        scoreCardTitle.textContent = '话天地阶段评分';
    }
    
    // 切换评分显示项（这会重置所有显示值）
    switchScoreDisplay('话天地');
    
    // 重置反馈内容
    feedbackContent.textContent = '开始对话后将显示评分反馈';
    
    // 隐藏本轮得分变化
    roundScoreChanges.style.display = 'none';
    scoreChangesContent.innerHTML = '';
    
    console.log('话天地阶段评分初始化完成');
}

function switchScoreDisplay(stage) {
    console.log(`切换评分显示到阶段: ${stage}`);
    
    // 隐藏所有评分项
    const allScoreItems = document.querySelectorAll('#realtime-scores .score-item');
    allScoreItems.forEach(item => {
        item.style.display = 'none';
    });
    
    // 显示当前阶段的评分项
    const currentStageItems = document.querySelectorAll(`#realtime-scores .score-item[data-stage="${stage}"]`);
    console.log(`找到 ${currentStageItems.length} 个 ${stage} 阶段的评分项`);
    
    currentStageItems.forEach(item => {
        item.style.display = 'flex';
        // 重置评分项的显示值为0
        const scoreElement = item.querySelector('.score-value');
        if (scoreElement) {
            scoreElement.textContent = '0/100';
            scoreElement.className = 'score-value poor';
            console.log(`重置评分项: ${item.querySelector('span').textContent}`);
        }
    });
    
    // 强制重置对话轮次和平均分显示
    if (conversationRoundsSpan) {
        conversationRoundsSpan.textContent = '0';
        console.log('重置对话轮次显示为0');
    }
    if (averageScoreSpan) {
        averageScoreSpan.textContent = '0';
        console.log('重置平均分显示为0');
    }
    
    // 重置进度条
    if (progressFill) {
        progressFill.style.width = '0%';
        console.log('重置进度条为0%');
    }
    
    console.log(`评分显示切换到 ${stage} 阶段完成`);
}

function updateConversationRounds() {
    conversationRoundsSpan.textContent = conversationRounds;
    
    // 不在这里处理第10轮的结束逻辑，让评分完成后再处理
    // 第10轮的结束检查将在evaluateChatStageMessage完成后进行
}

function calculateAverageScore() {
    if (currentStage === '话天地') {
        const scores = Object.values(chatStageScores);
        const sum = scores.reduce((a, b) => a + b, 0);
        return Math.round(sum / scores.length);
    } else if (currentStage === '人选介绍') {
        return calculateCandidateIntroAverageScore();
    }
    return 0;
}

function updateRealtimeScoreDisplay() {
    if (currentStage === '话天地') {
        // 更新话天地阶段各项评分显示
        for (const [criteria, score] of Object.entries(chatStageScores)) {
            const scoreElement = document.getElementById(`score-${criteria}`);
            if (scoreElement) {
                const scoreClass = score >= 80 ? 'good' : score >= 60 ? 'average' : 'poor';
                scoreElement.textContent = `${score}/100`;
                scoreElement.className = `score-value ${scoreClass}`;
            }
        }
        
        // 更新平均分
        const averageScore = calculateAverageScore();
        averageScoreSpan.textContent = averageScore;
        
        // 更新进度条
        const progressPercent = Math.min((averageScore / 75) * 100, 100);
        progressFill.style.width = `${progressPercent}%`;
        
        // 检查是否通过（只在未达到15轮且未完成时检查）
        if (averageScore >= 75 && conversationRounds > 0 && conversationRounds < 15 && !window.chatStageCompleted) {
            console.log(`第${conversationRounds}轮达到通过分数，准备完成阶段`);
            window.chatStageCompleted = true; // 设置标志位
            handleChatStageCompleted();
        }
    } else if (currentStage === '人选介绍') {
        // 如果是人选介绍阶段，调用专门的函数
        updateCandidateIntroScoreDisplay();
    }
}

async function evaluateChatStageMessage(message) {
    try {
        const response = await fetch('/api/evaluate-chat-stage', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: currentSessionId,
                message: message,
                conversation_rounds: conversationRounds
            })
        });

        if (!response.ok) {
            throw new Error('评分请求失败');
        }

        const evaluation = await response.json();
        
        // 检查是否已经设置了完成标志位，如果已完成则不再处理评分
        if (window.chatStageCompleted) {
            console.log('阶段已完成，跳过评分处理');
            return;
        }
        
        // 计算本轮得分变化并累积到总分
        console.log(`第${conversationRounds}轮评分开始`);
        console.log('评分前的累积分数:', { ...chatStageScores });
        console.log('本轮得分变化:', evaluation.score_changes);
        
        const scoreChanges = {};
        for (const [criteria, changeScore] of Object.entries(evaluation.score_changes)) {
            if (chatStageScores.hasOwnProperty(criteria)) {
                scoreChanges[criteria] = changeScore;
                const oldScore = chatStageScores[criteria];
                chatStageScores[criteria] = Math.max(0, Math.min(100, chatStageScores[criteria] + changeScore));
                console.log(`${criteria}: ${oldScore} + ${changeScore} = ${chatStageScores[criteria]}`);
            }
        }
        
        console.log('评分后的累积分数:', { ...chatStageScores });
        console.log('当前平均分:', calculateAverageScore());
        
        // 显示本轮得分变化
        displayScoreChanges(scoreChanges);
        
        // 更新显示
        updateRealtimeScoreDisplay();
        
        // 显示反馈
        if (evaluation.feedback) {
            feedbackContent.innerHTML = `
                <div class="feedback-suggestion">
                    ${evaluation.feedback}
                </div>
            `;
        }
        
        // 检查是否达到第15轮，如果是则进行最终判断
        if (conversationRounds >= 15 && !window.chatStageCompleted) {
            console.log(`第${conversationRounds}轮评分完成，进行最终判断`);
            const finalAverageScore = calculateAverageScore();
            console.log(`最终平均分: ${finalAverageScore}`);
            
            window.chatStageCompleted = true; // 设置标志位防止重复调用
            
            if (finalAverageScore >= 75) {
                // 通过
                handleChatStageCompleted();
            } else {
                // 失败
                handleChatStageFailed();
            }
        }
        
    } catch (error) {
        console.error('评分失败:', error);
        feedbackContent.innerHTML = `
            <div style="color: #dc3545;">
                评分失败: ${error.message}
            </div>
        `;
    }
}

function displayScoreChanges(scoreChanges) {
    let html = '';
    let hasChanges = false;
    
    for (const [criteria, change] of Object.entries(scoreChanges)) {
        if (change !== 0) {
            hasChanges = true;
            const changeClass = change > 0 ? 'score-change-positive' : 'score-change-negative';
            const changeSymbol = change > 0 ? '+' : '';
            html += `
                <div class="score-change-item">
                    <span>${criteria}</span>
                    <span class="${changeClass}">${changeSymbol}${change}</span>
                </div>
            `;
        } else {
            html += `
                <div class="score-change-item">
                    <span>${criteria}</span>
                    <span class="score-change-neutral">0</span>
                </div>
            `;
        }
    }
    
    if (hasChanges || conversationRounds === 1) {
        scoreChangesContent.innerHTML = html;
        roundScoreChanges.style.display = 'block';
    }
}

function showChatStageEvaluationResult(passed, finalAverageScore, suggestions, feedback) {
    // 构建评分数据，格式与开场白阶段保持一致
    const evaluation = {
        criteria_scores: { ...chatStageScores },
        average_score: finalAverageScore,
        passed: passed,
        suggestions: suggestions,
        feedback: feedback
    };

    let html = '<div class="evaluation-content">';

    // 显示各项评分
    html += '<h4>评分详情:</h4>';
    for (const [criteria, score] of Object.entries(evaluation.criteria_scores)) {
        const scoreClass = score >= 80 ? 'good' : score >= 60 ? 'average' : 'poor';
        html += `
            <div class="score-item">
                <span>${criteria}</span>
                <span class="score-value ${scoreClass}">${score}/100</span>
            </div>
        `;
    }

    // 显示平均分
    const avgClass = evaluation.average_score >= 80 ? 'good' : evaluation.average_score >= 60 ? 'average' : 'poor';
    html += `
        <div class="average-score">
            平均分: ${evaluation.average_score}/100
            ${evaluation.passed ? '✅ 通过' : '❌ 未通过'}
        </div>
    `;

    // 显示改进建议
    if (evaluation.suggestions && evaluation.suggestions.length > 0) {
        html += '<div class="suggestions">';
        html += '<h4>改进建议:</h4>';
        html += '<ul>';
        for (const suggestion of evaluation.suggestions) {
            html += `<li>${suggestion}</li>`;
        }
        html += '</ul>';
        html += '</div>';
    }

    // 显示总体评价
    if (evaluation.feedback) {
        html += `<div class="feedback"><h4>总体评价:</h4><p>${evaluation.feedback}</p></div>`;
    }

    html += '</div>';

    document.getElementById('evaluation-content').innerHTML = html;

    // 显示按钮
    if (passed) {
        // 话天地通过后显示继续按钮，隐藏重试按钮
        continueBtn.style.display = 'inline-block';
        continueBtn.textContent = '进入下一阶段';
        retryBtn.style.display = 'none';
        addMessage('system', '🎉 恭喜！话天地阶段通过！');
    } else {
        // 话天地失败后只显示重试按钮，隐藏继续按钮
        continueBtn.style.display = 'none';
        retryBtn.style.display = 'inline-block';
        retryBtn.textContent = '重新练习';
        addMessage('system', '⚠️ 话天地阶段未通过，请重新练习。');
    }

    // 显示历史记录按钮
    toggleHistoryBtn.style.display = 'inline-block';
    updateHistoryPanel();

    // 显示评分面板
    evaluationPanel.style.display = 'block';
    evaluationPanel.scrollIntoView({ behavior: 'smooth' });
    
    // 保持实时评分卡片显示，展示最终评分状态
    // realtimeScoreCard.style.display = 'none'; // 注释掉这行，保持评分卡片显示
}

function handleChatStageCompleted() {
    console.log('话天地阶段完成！');
    console.log('当前累积分数:', chatStageScores);
    
    // 重置等待响应状态
    isWaitingForResponse = false;
    
    // 计算并保存最终平均分（不重置累积分数）
    const finalAverageScore = calculateAverageScore();
    console.log('最终平均分数:', finalAverageScore);
    
    // 保存到历史记录
    const historyItem = {
        stage: '话天地',
        evaluation: {
            criteria_scores: { ...chatStageScores },
            average_score: finalAverageScore,
            passed: true,
            suggestions: [
                '恭喜通过话天地阶段！您成功建立了与客户的信任关系。',
                '在需求挖掘方面表现出色，能够深入了解客户需求。',
                '共情能力展现良好，能够理解客户的感受和顾虑。',
                '专业度体现充分，展现了红娘的专业素养。'
            ],
            feedback: '您在话天地阶段表现出色，成功建立了与客户的信任关系。通过有效的沟通技巧，您展现了专业的红娘素养，为后续的人选介绍阶段奠定了良好基础。'
        },
        timestamp: new Date().toLocaleString('zh-CN')
    };
    evaluationHistory.push(historyItem);
    
    // 禁用输入框，防止继续对话
    const userInput = document.getElementById('chat-input');
    const sendButton = document.getElementById('send-btn');
    if (userInput) userInput.disabled = true;
    if (sendButton) sendButton.disabled = true;
    
    // 显示评分报告
    showChatStageEvaluationResult(
        true,
        finalAverageScore,
        historyItem.evaluation.suggestions,
        historyItem.evaluation.feedback
    );
}

function handleChatStageFailed() {
    console.log('话天地阶段失败！');
    console.log('当前累积分数:', chatStageScores);
    
    // 重置等待响应状态
    isWaitingForResponse = false;
    
    // 计算并保存最终平均分（不重置累积分数）
    const finalAverageScore = calculateAverageScore();
    console.log('最终平均分数:', finalAverageScore);
    
    // 生成针对性的改进建议
    const suggestions = [];
    for (const [criteria, score] of Object.entries(chatStageScores)) {
        if (score < 60) {
            switch (criteria) {
                case '需求挖掘深度':
                    suggestions.push('需要更深入地了解客户的真实需求和期望，多问开放性问题。');
                    break;
                case '共情能力展现':
                    suggestions.push('要更好地理解客户的感受，表达同理心和关怀。');
                    break;
                case '疑虑处理效果':
                    suggestions.push('需要更有效地识别和处理客户的疑虑和担忧。');
                    break;
                case '专业度体现':
                    suggestions.push('要展现更多的专业知识和经验，提升客户信任度。');
                    break;
                case '对话节奏把控':
                    suggestions.push('需要更好地控制对话节奏，适时推进或放缓。');
                    break;
                case '信任关系建立':
                    suggestions.push('要更积极地建立与客户的信任关系，增强亲和力。');
                    break;
            }
        }
    }
    
    if (suggestions.length === 0) {
        suggestions.push('虽然各项分数都不错，但整体平均分未达到通过标准，建议继续练习提升。');
    }
    
    // 保存到历史记录
    const historyItem = {
        stage: '话天地',
        evaluation: {
            criteria_scores: { ...chatStageScores },
            average_score: finalAverageScore,
            passed: false,
            suggestions: suggestions,
            feedback: `话天地阶段未通过（平均分：${finalAverageScore}/100，需要达到75分）。经过15轮对话，您在某些方面还需要加强。建议重新练习，重点关注得分较低的评分标准。`
        },
        timestamp: new Date().toLocaleString('zh-CN')
    };
    evaluationHistory.push(historyItem);
    
    // 禁用输入框，防止继续对话
    const userInput = document.getElementById('chat-input');
    const sendButton = document.getElementById('send-btn');
    if (userInput) userInput.disabled = true;
    if (sendButton) sendButton.disabled = true;
    
    // 显示评分报告
    showChatStageEvaluationResult(
        false,
        finalAverageScore,
        historyItem.evaluation.suggestions,
        historyItem.evaluation.feedback
    );
}

function restartChatStage() {
    console.log('重新开始话天地阶段');
    
    // 重置评分数据
    conversationRounds = 0;
    chatStageScores = {
        '需求挖掘深度': 0,
        '共情能力展现': 0,
        '疑虑处理效果': 0,
        '专业度体现': 0,
        '对话节奏把控': 0,
        '信任关系建立': 0,
        '扣题程度': 0
    };
    
    // 重置标志位
    window.chatStageCompleted = false;
    isWaitingForResponse = false;
    
    // 隐藏评分面板
    evaluationPanel.style.display = 'none';
    
    // 显示实时评分卡片
    realtimeScoreCard.style.display = 'flex';
    realtimeScoreCard.style.flexDirection = 'column';
    realtimeScoreCard.style.visibility = 'visible';
    realtimeScoreCard.style.opacity = '1';
    
    // 切换评分显示项
    switchScoreDisplay('话天地');
    
    // 更新对话轮次显示
    updateConversationRounds();
    
    // 更新显示
    updateRealtimeScoreDisplay();
    
    // 重置反馈内容
    feedbackContent.textContent = '请重新开始话天地阶段的练习';
    
    // 隐藏本轮得分变化
    roundScoreChanges.style.display = 'none';
    scoreChangesContent.innerHTML = '';
    
    // 更新评分卡片标题
    const scoreCardTitle = document.getElementById('score-card-title');
    if (scoreCardTitle) {
        scoreCardTitle.textContent = '话天地阶段评分';
    }
    
    // 重新启用输入框
    const userInput = document.getElementById('chat-input');
    const sendButton = document.getElementById('send-btn');
    if (userInput) {
        userInput.disabled = false;
        userInput.focus();
    }
    if (sendButton) sendButton.disabled = false;
    
    addMessage('system', '🔄 话天地阶段已重置，请重新开始练习。');
}

function showEvaluationLoading() {
    // 显示评分面板
    evaluationPanel.style.display = 'block';
    
    // 显示加载状态
    document.getElementById('evaluation-content').innerHTML = `
        <div class="evaluation-loading">
            <div class="loading-spinner">
                <div class="loading"></div>
            </div>
            <p>正在生成评分报告，请稍候...</p>
        </div>
    `;
    
    evaluationPanel.scrollIntoView({ behavior: 'smooth' });
}

function hideEvaluationLoading() {
    // 清除加载状态，准备显示评分结果
    // showEvaluationResult函数会重新填充内容
}

// 人选介绍阶段相关函数
function initializeCandidateIntroScoring() {
    console.log('=== 开始初始化人选介绍阶段评分 ===');
    
    // 重置评分数据
    conversationRounds = 0;
    candidateIntroScores = {
        '匹配精准度': 0,
        '信息具体性': 0,
        '多样性展示': 0,
        '生活化描述': 0,
        '互动性保持': 0,
        '真实感营造': 0,
        '扣题程度': 0
    };
    console.log('重置人选介绍评分数据:', candidateIntroScores);
    
    // 使用延迟确保DOM完全加载后再执行重置
    setTimeout(() => {
        // 1. 强制更新标题
        const scoreCardTitle = document.getElementById('score-card-title');
        if (scoreCardTitle) {
            console.log('找到标题元素，当前内容:', scoreCardTitle.textContent);
            scoreCardTitle.textContent = '人选介绍阶段评分';
            scoreCardTitle.innerHTML = '人选介绍阶段评分';
            console.log('强制更新标题为:', scoreCardTitle.textContent);
        } else {
            console.error('未找到标题元素');
        }
        
        // 2. 强制重置对话轮次和平均分
        const roundsElement = document.getElementById('conversation-rounds');
        const avgElement = document.getElementById('average-score');
        const progressElement = document.getElementById('progress-fill');
        
        if (roundsElement) {
            roundsElement.textContent = '0';
            console.log('重置对话轮次显示为0');
        }
        if (avgElement) {
            avgElement.textContent = '0';
            console.log('重置平均分显示为0');
        }
        if (progressElement) {
            progressElement.style.width = '0%';
            console.log('重置进度条为0%');
        }
        
        // 3. 强制隐藏话天地评分项
        const chatStageItems = document.querySelectorAll('[data-stage="话天地"]');
        console.log('找到话天地评分项数量:', chatStageItems.length);
        chatStageItems.forEach(item => {
            item.style.display = 'none';
            const labelElement = item.querySelector('span');
            if (labelElement) {
                console.log('隐藏话天地评分项:', labelElement.textContent);
            }
        });
        
        // 4. 强制显示人选介绍评分项并重置为0
        const candidateIntroItems = document.querySelectorAll('[data-stage="人选介绍"]');
        console.log('找到人选介绍评分项数量:', candidateIntroItems.length);
        candidateIntroItems.forEach(item => {
            item.style.display = 'flex';
            const scoreElement = item.querySelector('.score-value');
            if (scoreElement) {
                scoreElement.textContent = '0/100';
                scoreElement.className = 'score-value poor';
            }
            const labelElement = item.querySelector('span');
            if (labelElement) {
                console.log('显示并重置人选介绍评分项:', labelElement.textContent);
            }
        });
        
        // 5. 重置反馈和得分变化
        const feedbackElement = document.getElementById('feedback-content');
        const roundScoreElement = document.getElementById('round-score-changes');
        const scoreChangesElement = document.getElementById('score-changes-content');
        
        if (feedbackElement) {
            feedbackElement.textContent = '开始介绍候选人后将显示评分反馈';
        }
        if (roundScoreElement) {
            roundScoreElement.style.display = 'none';
        }
        if (scoreChangesElement) {
            scoreChangesElement.innerHTML = '';
        }
        
        console.log('=== 人选介绍阶段显示重置完成 ===');
    }, 100);
    
    console.log('=== 人选介绍阶段评分初始化完成 ===');
}

function updateCandidateIntroScoreDisplay() {
    // 更新各项评分显示
    for (const [criteria, score] of Object.entries(candidateIntroScores)) {
        const scoreElement = document.getElementById(`score-${criteria}`);
        if (scoreElement) {
            const scoreClass = score >= 80 ? 'good' : score >= 60 ? 'average' : 'poor';
            scoreElement.textContent = `${score}/100`;
            scoreElement.className = `score-value ${scoreClass}`;
        }
    }
    
    // 更新平均分
    const averageScore = calculateCandidateIntroAverageScore();
    averageScoreSpan.textContent = averageScore;
    
    // 更新进度条
    const progressPercent = Math.min((averageScore / 75) * 100, 100);
    progressFill.style.width = `${progressPercent}%`;
    
    // 检查是否通过（只在未达到15轮且未完成时检查）
    if (averageScore >= 75 && conversationRounds > 0 && conversationRounds < 15 && !window.candidateIntroCompleted) {
        console.log(`第${conversationRounds}轮达到通过分数，准备完成阶段`);
        window.candidateIntroCompleted = true; // 设置标志位
        handleCandidateIntroCompleted();
    }
}

function calculateCandidateIntroAverageScore() {
    const scores = Object.values(candidateIntroScores);
    const sum = scores.reduce((a, b) => a + b, 0);
    return Math.round(sum / scores.length);
}

async function evaluateCandidateIntroMessage(message) {
    try {
        const response = await fetch('/api/evaluate-candidate-intro', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: currentSessionId,
                message: message,
                conversation_rounds: conversationRounds
            })
        });

        if (!response.ok) {
            throw new Error('评分请求失败');
        }

        const evaluation = await response.json();
        
        // 检查是否已经设置了完成标志位，如果已完成则不再处理评分
        if (window.candidateIntroCompleted) {
            console.log('阶段已完成，跳过评分处理');
            return;
        }
        
        // 计算本轮得分变化并累积到总分
        console.log(`第${conversationRounds}轮评分开始`);
        console.log('评分前的累积分数:', { ...candidateIntroScores });
        console.log('本轮得分变化:', evaluation.score_changes);
        
        const scoreChanges = {};
        for (const [criteria, changeScore] of Object.entries(evaluation.score_changes)) {
            if (candidateIntroScores.hasOwnProperty(criteria)) {
                scoreChanges[criteria] = changeScore;
                const oldScore = candidateIntroScores[criteria];
                candidateIntroScores[criteria] = Math.max(0, Math.min(100, candidateIntroScores[criteria] + changeScore));
                console.log(`${criteria}: ${oldScore} + ${changeScore} = ${candidateIntroScores[criteria]}`);
            }
        }
        
        console.log('评分后的累积分数:', { ...candidateIntroScores });
        console.log('当前平均分:', calculateCandidateIntroAverageScore());
        
        // 显示本轮得分变化
        displayCandidateIntroScoreChanges(scoreChanges);
        
        // 更新显示
        updateCandidateIntroScoreDisplay();
        
        // 显示反馈
        if (evaluation.feedback) {
            feedbackContent.innerHTML = `
                <div class="feedback-suggestion">
                    ${evaluation.feedback}
                </div>
            `;
        }
        
        // 检查是否达到第15轮，如果是则进行最终判断
        if (conversationRounds >= 15 && !window.candidateIntroCompleted) {
            console.log(`第${conversationRounds}轮评分完成，进行最终判断`);
            const finalAverageScore = calculateCandidateIntroAverageScore();
            console.log(`最终平均分: ${finalAverageScore}`);
            
            window.candidateIntroCompleted = true; // 设置标志位防止重复调用
            
            if (finalAverageScore >= 75) {
                // 通过
                handleCandidateIntroCompleted();
            } else {
                // 失败
                handleCandidateIntroFailed();
            }
        }
        
    } catch (error) {
        console.error('评分失败:', error);
        feedbackContent.innerHTML = `
            <div style="color: #dc3545;">
                评分失败: ${error.message}
            </div>
        `;
    }
}

function displayCandidateIntroScoreChanges(scoreChanges) {
    let html = '';
    let hasChanges = false;
    
    for (const [criteria, change] of Object.entries(scoreChanges)) {
        if (change !== 0) {
            hasChanges = true;
            const changeClass = change > 0 ? 'score-change-positive' : 'score-change-negative';
            const changeSymbol = change > 0 ? '+' : '';
            html += `
                <div class="score-change-item">
                    <span>${criteria}</span>
                    <span class="${changeClass}">${changeSymbol}${change}</span>
                </div>
            `;
        } else {
            html += `
                <div class="score-change-item">
                    <span>${criteria}</span>
                    <span class="score-change-neutral">0</span>
                </div>
            `;
        }
    }
    
    if (hasChanges || conversationRounds === 1) {
        scoreChangesContent.innerHTML = html;
        roundScoreChanges.style.display = 'block';
    }
}

function handleCandidateIntroCompleted() {
    console.log('人选介绍阶段完成！');
    console.log('当前累积分数:', candidateIntroScores);
    
    // 重置等待响应状态
    isWaitingForResponse = false;
    
    // 计算并保存最终平均分
    const finalAverageScore = calculateCandidateIntroAverageScore();
    console.log('最终平均分数:', finalAverageScore);
    
    // 保存到历史记录
    const historyItem = {
        stage: '人选介绍',
        evaluation: {
            criteria_scores: { ...candidateIntroScores },
            average_score: finalAverageScore,
            passed: true,
            suggestions: [
                '恭喜通过人选介绍阶段！您成功向客户展示了合适的候选人。',
                '在匹配精准度方面表现出色，能够准确把握客户需求。',
                '信息具体性良好，提供了详细的候选人信息。',
                '多样性展示充分，给客户提供了多种选择。'
            ],
            feedback: '您在人选介绍阶段表现出色，成功向客户展示了合适的候选人。通过精准的匹配和生动的描述，您让客户对候选人产生了兴趣，为后续的深入沟通奠定了良好基础。'
        },
        timestamp: new Date().toLocaleString('zh-CN')
    };
    evaluationHistory.push(historyItem);
    
    // 禁用输入框，防止继续对话
    const userInput = document.getElementById('chat-input');
    const sendButton = document.getElementById('send-btn');
    if (userInput) userInput.disabled = true;
    if (sendButton) sendButton.disabled = true;
    
    // 显示评分报告
    showCandidateIntroEvaluationResult(
        true,
        finalAverageScore,
        historyItem.evaluation.suggestions,
        historyItem.evaluation.feedback
    );
}

function handleCandidateIntroFailed() {
    console.log('人选介绍阶段失败！');
    console.log('当前累积分数:', candidateIntroScores);
    
    // 重置等待响应状态
    isWaitingForResponse = false;
    
    // 计算并保存最终平均分
    const finalAverageScore = calculateCandidateIntroAverageScore();
    console.log('最终平均分数:', finalAverageScore);
    
    // 生成针对性的改进建议
    const suggestions = [];
    for (const [criteria, score] of Object.entries(candidateIntroScores)) {
        if (score < 60) {
            switch (criteria) {
                case '匹配精准度':
                    suggestions.push('需要更准确地把握客户需求，介绍更符合要求的候选人。');
                    break;
                case '信息具体性':
                    suggestions.push('要提供更具体详细的候选人信息，避免模糊描述。');
                    break;
                case '多样性展示':
                    suggestions.push('需要介绍多个不同类型的候选人，给客户更多选择。');
                    break;
                case '生活化描述':
                    suggestions.push('要更生动地描绘候选人的生活场景，增强真实感。');
                    break;
                case '互动性保持':
                    suggestions.push('需要更多地询问客户意见，根据反馈调整介绍方向。');
                    break;
                case '真实感营造':
                    suggestions.push('要让客户相信这些候选人是真实存在的，增强可信度。');
                    break;
                case '扣题程度':
                    suggestions.push('需要更好地回应客户的问题和关注点，避免跑题。');
                    break;
            }
        }
    }
    
    if (suggestions.length === 0) {
        suggestions.push('虽然各项分数都不错，但整体平均分未达到通过标准，建议继续练习提升。');
    }
    
    // 保存到历史记录
    const historyItem = {
        stage: '人选介绍',
        evaluation: {
            criteria_scores: { ...candidateIntroScores },
            average_score: finalAverageScore,
            passed: false,
            suggestions: suggestions,
            feedback: `人选介绍阶段未通过（平均分：${finalAverageScore}/100，需要达到75分）。经过15轮对话，您在某些方面还需要加强。建议重新练习，重点关注得分较低的评分标准。`
        },
        timestamp: new Date().toLocaleString('zh-CN')
    };
    evaluationHistory.push(historyItem);
    
    // 禁用输入框，防止继续对话
    const userInput = document.getElementById('chat-input');
    const sendButton = document.getElementById('send-btn');
    if (userInput) userInput.disabled = true;
    if (sendButton) sendButton.disabled = true;
    
    // 显示评分报告
    showCandidateIntroEvaluationResult(
        false,
        finalAverageScore,
        historyItem.evaluation.suggestions,
        historyItem.evaluation.feedback
    );
}

function showCandidateIntroEvaluationResult(passed, finalAverageScore, suggestions, feedback) {
    // 构建评分数据，格式与其他阶段保持一致
    const evaluation = {
        criteria_scores: { ...candidateIntroScores },
        average_score: finalAverageScore,
        passed: passed,
        suggestions: suggestions,
        feedback: feedback
    };

    let html = '<div class="evaluation-content">';

    // 显示各项评分
    html += '<h4>评分详情:</h4>';
    for (const [criteria, score] of Object.entries(evaluation.criteria_scores)) {
        const scoreClass = score >= 80 ? 'good' : score >= 60 ? 'average' : 'poor';
        html += `
            <div class="score-item">
                <span>${criteria}</span>
                <span class="score-value ${scoreClass}">${score}/100</span>
            </div>
        `;
    }

    // 显示平均分
    const avgClass = evaluation.average_score >= 80 ? 'good' : evaluation.average_score >= 60 ? 'average' : 'poor';
    html += `
        <div class="average-score">
            平均分: ${evaluation.average_score}/100
            ${evaluation.passed ? '✅ 通过' : '❌ 未通过'}
        </div>
    `;

    // 显示改进建议
    if (evaluation.suggestions && evaluation.suggestions.length > 0) {
        html += '<div class="suggestions">';
        html += '<h4>改进建议:</h4>';
        html += '<ul>';
        for (const suggestion of evaluation.suggestions) {
            html += `<li>${suggestion}</li>`;
        }
        html += '</ul>';
        html += '</div>';
    }

    // 显示总体评价
    if (evaluation.feedback) {
        html += `<div class="feedback"><h4>总体评价:</h4><p>${evaluation.feedback}</p></div>`;
    }

    html += '</div>';

    document.getElementById('evaluation-content').innerHTML = html;

    // 显示按钮
    if (passed) {
        // 人选介绍通过后显示继续按钮，隐藏重试按钮
        continueBtn.style.display = 'inline-block';
        continueBtn.textContent = '进入下一阶段';
        retryBtn.style.display = 'none';
        addMessage('system', '🎉 恭喜！人选介绍阶段通过！');
    } else {
        // 人选介绍失败后只显示重试按钮，隐藏继续按钮
        continueBtn.style.display = 'none';
        retryBtn.style.display = 'inline-block';
        retryBtn.textContent = '重新练习';
        addMessage('system', '⚠️ 人选介绍阶段未通过，请重新练习。');
    }

    // 显示历史记录按钮
    toggleHistoryBtn.style.display = 'inline-block';
    updateHistoryPanel();

    // 显示评分面板
    evaluationPanel.style.display = 'block';
    evaluationPanel.scrollIntoView({ behavior: 'smooth' });
}

function restartCandidateIntroStage() {
    console.log('重新开始人选介绍阶段');
    
    // 重置评分数据
    conversationRounds = 0;
    candidateIntroScores = {
        '匹配精准度': 0,
        '信息具体性': 0,
        '多样性展示': 0,
        '生活化描述': 0,
        '互动性保持': 0,
        '真实感营造': 0,
        '扣题程度': 0
    };
    
    // 重置标志位
    window.candidateIntroCompleted = false;
    isWaitingForResponse = false;
    
    // 隐藏评分面板
    evaluationPanel.style.display = 'none';
    
    // 显示实时评分卡片
    realtimeScoreCard.style.display = 'flex';
    realtimeScoreCard.style.flexDirection = 'column';
    realtimeScoreCard.style.visibility = 'visible';
    realtimeScoreCard.style.opacity = '1';
    
    // 切换评分显示项
    switchScoreDisplay('人选介绍');
    
    // 更新对话轮次显示
    updateConversationRounds();
    
    // 更新显示
    updateCandidateIntroScoreDisplay();
    
    // 重置反馈内容
    feedbackContent.textContent = '请重新开始人选介绍阶段的练习';
    
    // 隐藏本轮得分变化
    roundScoreChanges.style.display = 'none';
    scoreChangesContent.innerHTML = '';
    
    // 更新评分卡片标题
    const scoreCardTitle = document.getElementById('score-card-title');
    if (scoreCardTitle) {
        scoreCardTitle.textContent = '人选介绍阶段评分';
    }
    
    // 重新启用输入框
    forceEnableInputBox();
    
    // 延迟再次确保启用
    setTimeout(() => {
        forceEnableInputBox();
    }, 100);
    
    addMessage('system', '🔄 人选介绍阶段已重置，请重新开始练习。');
}
