// 全局变量
let currentSessionId = null;
let currentStage = '开场白';
let isWaitingForResponse = false;
let stageHistory = {}; // 存储各阶段的评分历史

// 阶段评分标准定义
const STAGE_CRITERIA = {
    '开场白': ['自我介绍明确性', '来电目的说明', '获号渠道说明', '匹配点提及', '避免强推话术', '语气自然度'],
    '话天地': ['需求挖掘深度', '共情能力展现', '疑虑处理效果', '专业度体现', '对话节奏把控', '信任关系建立'],
    '人选介绍': ['匹配精准度', '信息具体性', '多样性展示', '生活化描述', '互动性保持', '真实感营造'],
    '入主题': ['安全感营造', '流程说明清晰', '必要性阐述', '身份验证强调', '顾虑消除效果', '引导自然度'],
    '促成': ['时机把握', '细节确认', '诚意表达', '压力控制', '决策引导', '专业收尾']
};

// DOM元素
const configPanel = document.getElementById('config-panel');
const chatPanel = document.getElementById('chat-panel');
const evaluationPanel = document.getElementById('evaluation-panel');
const customerForm = document.getElementById('customer-form');
const chatMessages = document.getElementById('chat-messages');
const chatInput = document.getElementById('chat-input');
const sendBtn = document.getElementById('send-btn');
const currentStageSpan = document.getElementById('current-stage');
const continueBtn = document.getElementById('continue-btn');
const retryBtn = document.getElementById('retry-btn');
const evaluationTitle = document.getElementById('evaluation-title');
const currentEvaluation = document.getElementById('current-evaluation');
const toggleHistoryBtn = document.getElementById('toggle-history-btn');
const historyPanel = document.getElementById('history-panel');
const historyContent = document.getElementById('history-content');
const closeHistoryBtn = document.getElementById('close-history-btn');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

function setupEventListeners() {
    // 表单提交
    customerForm.addEventListener('submit', handleFormSubmit);

    // 发送消息
    sendBtn.addEventListener('click', sendMessage);
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // 按钮事件
    continueBtn.addEventListener('click', continueToNextStage);
    retryBtn.addEventListener('click', retryCurrentStage);
    toggleHistoryBtn.addEventListener('click', toggleHistoryPanel);
    closeHistoryBtn.addEventListener('click', closeHistoryPanel);
}

async function handleFormSubmit(e) {
    e.preventDefault();

    const formData = new FormData(customerForm);
    const customerProfile = {};

    for (let [key, value] of formData.entries()) {
        customerProfile[key] = value;
    }

    try {
        const response = await fetch('/api/start-session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(customerProfile)
        });

        if (!response.ok) {
            throw new Error('Failed to start session');
        }

        const data = await response.json();
        currentSessionId = data.session_id;
        currentStage = data.current_stage;

        // 切换到聊天界面
        configPanel.style.display = 'none';
        chatPanel.style.display = 'block';

        // 添加系统消息
        addMessage('system', data.message);

        // 更新阶段显示
        updateStageDisplay();
        updateEvaluationSection();

    } catch (error) {
        alert('启动会话失败: ' + error.message);
    }
}

async function sendMessage() {
    if (isWaitingForResponse || !chatInput.value.trim()) {
        return;
    }

    const message = chatInput.value.trim();
    chatInput.value = '';

    // 添加按钮点击动画效果
    sendBtn.classList.add('btn-clicked');
    setTimeout(() => {
        sendBtn.classList.remove('btn-clicked');
    }, 200);

    // 添加红娘消息
    addMessage('matchmaker', message);

    try {
        if (currentStage === '开场白') {
            // 显示报告生成提示
            showReportGenerating();
            await handleOpeningStage(message);
            // 隐藏报告生成提示
            hideReportGenerating();
        } else {
            await handleConversationStage(message);
        }
    } catch (error) {
        addMessage('system', '发送失败: ' + error.message);
        hideReportGenerating();
    }
}

async function handleOpeningStage(message) {
    // 首先获取客户回复
    const customerResponse = await getCustomerResponse(message);

    // 显示客户回复
    addMessage('customer', customerResponse);

    // 然后进行评分
    const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_id: currentSessionId,
            message: message,
            stage: currentStage
        })
    });

    if (!response.ok) {
        throw new Error('Request failed');
    }

    const data = await response.json();

    // 保存评分到历史记录
    saveEvaluationToHistory(currentStage, data.evaluation, message);

    // 显示评分结果
    showEvaluationResult(data);

    if (data.stage_completed) {
        currentStage = data.next_stage;
        updateStageDisplay();
        updateEvaluationSection();
    }
}

async function getCustomerResponse(message) {
    try {
        const response = await fetch('/api/customer-response', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: currentSessionId,
                message: message,
                stage: currentStage
            })
        });

        if (!response.ok) {
            throw new Error('Failed to get customer response');
        }

        const data = await response.json();
        return data.response;
    } catch (error) {
        console.error('获取客户回复失败:', error);
        return '客户正在思考中...';
    }
}

async function handleConversationStage(message) {
    const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_id: currentSessionId,
            message: message,
            stage: currentStage
        })
    });

    if (!response.ok) {
        throw new Error('Request failed');
    }

    // 处理流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let assistantMessage = '';
    let messageElement = null;

    while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
            if (line.startsWith('data: ')) {
                try {
                    const data = JSON.parse(line.slice(6));

                    if (data.type === 'content') {
                        if (!messageElement) {
                            messageElement = addMessage('customer', '');
                        }
                        assistantMessage += data.data;
                        messageElement.textContent = assistantMessage;
                        scrollToBottom();
                    } else if (data.type === 'complete') {
                        // 流式输出完成
                        break;
                    } else if (data.type === 'evaluation_trigger') {
                        // 触发实时评分
                        await handleRealtimeEvaluation(data.message);
                    }
                } catch (e) {
                    // 忽略解析错误
                }
            }
        }
    }
}

async function handleRealtimeEvaluation(message) {
    try {
        const response = await fetch('/api/evaluate-realtime', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: currentSessionId,
                message: message
            })
        });

        if (!response.ok) {
            throw new Error('Evaluation failed');
        }

        const data = await response.json();

        if (data.error) {
            console.error('评分错误:', data.error);
            return;
        }

        // 更新实时评分显示
        updateRealtimeScores(data);

    } catch (error) {
        console.error('实时评分失败:', error);
    }
}

function addMessage(role, content) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}`;

    // 创建消息内容结构
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';

    // 添加头像
    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';

    // 添加角色标识和内容
    const messageBody = document.createElement('div');
    messageBody.className = 'message-body';

    const roleLabel = document.createElement('div');
    roleLabel.className = 'message-role';

    const messageText = document.createElement('div');
    messageText.className = 'message-text';
    messageText.textContent = content;

    // 根据角色设置不同的显示
    if (role === 'user' || role === 'matchmaker') {
        // 红娘消息显示在右侧
        avatar.innerHTML = '👩‍💼';
        roleLabel.textContent = '红娘';
        messageDiv.classList.add('matchmaker-message');
    } else if (role === 'customer') {
        // 客户消息显示在左侧
        avatar.innerHTML = '👤';
        roleLabel.textContent = '客户';
        messageDiv.classList.add('customer-message');
    } else if (role === 'assistant') {
        // AI客户回复显示在左侧
        avatar.innerHTML = '👤';
        roleLabel.textContent = '客户';
        messageDiv.classList.add('customer-message');
    } else {
        // 系统消息居中显示
        avatar.innerHTML = '🤖';
        roleLabel.textContent = '系统';
        messageDiv.classList.add('system-message');
    }

    // 组装消息结构
    messageBody.appendChild(roleLabel);
    messageBody.appendChild(messageText);

    if (role === 'system') {
        // 系统消息只显示内容，不显示头像
        messageContent.appendChild(messageBody);
    } else {
        messageContent.appendChild(avatar);
        messageContent.appendChild(messageBody);
    }

    messageDiv.appendChild(messageContent);
    chatMessages.appendChild(messageDiv);
    scrollToBottom();
    return messageText; // 返回文本元素，用于流式更新
}

function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function showReportGenerating() {
    // 在页面下方显示报告生成提示
    const reportGeneratingDiv = document.createElement('div');
    reportGeneratingDiv.id = 'report-generating';
    reportGeneratingDiv.className = 'report-generating';
    reportGeneratingDiv.innerHTML = `
        <div class="generating-content">
            <div class="loading-spinner"></div>
            <span class="generating-text">报告生成中，请稍等...</span>
        </div>
    `;

    // 插入到评分面板之前
    const container = document.querySelector('.container');
    container.insertBefore(reportGeneratingDiv, evaluationPanel);

    // 滚动到提示位置
    reportGeneratingDiv.scrollIntoView({ behavior: 'smooth' });
}

function hideReportGenerating() {
    const reportGeneratingDiv = document.getElementById('report-generating');
    if (reportGeneratingDiv) {
        reportGeneratingDiv.remove();
    }
}

function updateStageDisplay() {
    currentStageSpan.textContent = currentStage;

    // 更新进度条
    const stageItems = document.querySelectorAll('.stage-item');
    stageItems.forEach(item => {
        item.classList.remove('active', 'completed');
        if (item.dataset.stage === currentStage) {
            item.classList.add('active');
        }
        // 标记已完成的阶段
        if (stageHistory[item.dataset.stage]) {
            item.classList.add('completed');
        }
    });
}

function updateEvaluationSection() {
    // 更新评分区域标题
    evaluationTitle.textContent = `${currentStage}阶段评分`;

    // 显示当前阶段的评分标准
    showCurrentStageCriteria();

    // 如果有历史记录，显示历史记录按钮
    if (Object.keys(stageHistory).length > 0) {
        toggleHistoryBtn.style.display = 'inline-block';
    }
}

function showCurrentStageCriteria() {
    const criteria = STAGE_CRITERIA[currentStage] || [];

    let html = '<div class="evaluation-placeholder">';
    if (currentStage === '开场白') {
        html += '<p>请输入开场白话术后查看评分结果</p>';
    } else {
        html += '<div class="realtime-evaluation">';
        html += '<div class="round-info">';
        html += '<span>对话轮次: <span id="conversation-rounds">0</span></span>';
        html += '<span>平均分: <span id="average-score">0</span>/100</span>';
        html += '</div>';

        html += '<div class="criteria-scores">';
        criteria.forEach(criterion => {
            html += `<div class="score-item">`;
            html += `<span>${criterion}</span>`;
            html += `<span class="score-value" id="score-${criterion}">0/100</span>`;
            html += `</div>`;
        });
        html += '</div>';

        html += '<div class="latest-feedback">';
        html += '<h5>最新反馈</h5>';
        html += '<div id="feedback-content">开始对话后将显示评分反馈</div>';
        html += '</div>';
        html += '</div>';
    }
    html += '</div>';

    currentEvaluation.innerHTML = html;
}

function updateRealtimeScores(data) {
    const evaluation = data.evaluation;
    const roundNumber = data.round_number;

    // 更新轮次信息
    const roundsSpan = document.getElementById('conversation-rounds');
    const avgScoreSpan = document.getElementById('average-score');

    if (roundsSpan) {
        roundsSpan.textContent = roundNumber;
    }

    if (avgScoreSpan) {
        avgScoreSpan.textContent = evaluation.average_score;
    }

    // 更新各项评分
    for (const [criteria, score] of Object.entries(evaluation.criteria_scores)) {
        const scoreElement = document.getElementById(`score-${criteria}`);
        if (scoreElement) {
            scoreElement.textContent = `${score}/100`;

            // 添加颜色样式
            scoreElement.className = 'score-value';
            if (score >= 80) {
                scoreElement.classList.add('good');
            } else if (score >= 60) {
                scoreElement.classList.add('average');
            } else {
                scoreElement.classList.add('poor');
            }
        }
    }

    // 更新反馈内容
    const feedbackElement = document.getElementById('feedback-content');
    if (feedbackElement) {
        let feedbackHtml = `<p><strong>第${roundNumber}轮评分:</strong> ${evaluation.average_score}/100</p>`;

        if (evaluation.suggestions && evaluation.suggestions.length > 0) {
            feedbackHtml += '<p><strong>改进建议:</strong></p>';
            feedbackHtml += '<ul>';
            evaluation.suggestions.forEach(suggestion => {
                feedbackHtml += `<li>${suggestion}</li>`;
            });
            feedbackHtml += '</ul>';
        }

        if (evaluation.feedback) {
            feedbackHtml += `<p><strong>总体评价:</strong> ${evaluation.feedback}</p>`;
        }

        feedbackElement.innerHTML = feedbackHtml;
    }

    // 保存到历史记录
    saveEvaluationToHistory(currentStage, evaluation, data.message || '');
}

function saveEvaluationToHistory(stage, evaluation, message) {
    if (!stageHistory[stage]) {
        stageHistory[stage] = [];
    }

    stageHistory[stage].push({
        timestamp: new Date().toLocaleString(),
        message: message,
        evaluation: evaluation
    });
}

function showEvaluationResult(data) {
    const evaluation = data.evaluation;

    let html = '<div class="evaluation-content">';

    // 显示各项评分
    html += '<h4>评分详情:</h4>';
    for (const [criteria, score] of Object.entries(evaluation.criteria_scores)) {
        const scoreClass = score >= 80 ? 'good' : score >= 60 ? 'average' : 'poor';
        html += `
            <div class="score-item">
                <span>${criteria}</span>
                <span class="score-value ${scoreClass}">${score}/100</span>
            </div>
        `;
    }

    // 显示平均分
    html += `
        <div class="average-score">
            平均分: ${evaluation.average_score}/100
            ${evaluation.passed ? '✅ 通过' : '❌ 未通过'}
        </div>
    `;

    // 显示改进建议
    if (evaluation.suggestions && evaluation.suggestions.length > 0) {
        html += '<div class="suggestions">';
        html += '<h4>改进建议:</h4>';
        html += '<ul>';
        for (const suggestion of evaluation.suggestions) {
            html += `<li>${suggestion}</li>`;
        }
        html += '</ul>';
        html += '</div>';
    }

    // 显示总体评价
    if (evaluation.feedback) {
        html += `<div class="feedback"><h4>总体评价:</h4><p>${evaluation.feedback}</p></div>`;
    }

    html += '</div>';

    currentEvaluation.innerHTML = html;

    // 显示按钮和评价报告
    if (data.stage_completed) {
        // 开场白通过后显示继续按钮
        continueBtn.style.display = 'inline-block';
        continueBtn.textContent = '进入下一阶段';
        addMessage('system', `🎉 ${data.message}`);

        // 显示评价报告在页面下方
        showOpeningEvaluationReport(data.evaluation);

    } else {
        // 开场白未通过，显示重试按钮
        continueBtn.style.display = 'none';
        retryBtn.style.display = 'inline-block';
        addMessage('system', `⚠️ ${data.message}`);

        // 显示评价报告在页面下方
        showOpeningEvaluationReport(data.evaluation);
    }

    // 显示评分面板
    evaluationPanel.style.display = 'block';
    evaluationPanel.scrollIntoView({ behavior: 'smooth' });
}

function showOpeningEvaluationReport(evaluation) {
    // 在评分面板中显示开场白评价报告
    let html = '<div class="opening-evaluation-report">';
    html += '<h3>📊 开场白阶段评价报告</h3>';

    // 总体得分
    html += '<div class="summary-score">';
    html += `<div class="total-score ${evaluation.passed ? 'passed' : 'failed'}">`;
    html += `<span class="score-label">总体得分</span>`;
    html += `<span class="score-value">${evaluation.average_score}/100</span>`;
    html += `<span class="pass-status">${evaluation.passed ? '✅ 通过' : '❌ 未通过'}</span>`;
    html += '</div>';
    html += '</div>';

    // 详细得分
    html += '<div class="detailed-scores">';
    html += '<h4>📈 详细得分</h4>';
    html += '<div class="scores-grid">';
    for (const [criteria, score] of Object.entries(evaluation.criteria_scores)) {
        const scoreClass = score >= 80 ? 'excellent' : score >= 70 ? 'good' : score >= 60 ? 'average' : 'poor';
        const emoji = score >= 80 ? '🌟' : score >= 70 ? '👍' : score >= 60 ? '⚠️' : '❌';
        html += `<div class="score-detail-item">`;
        html += `<div class="criteria-info">`;
        html += `<span class="criteria-name">${emoji} ${criteria}</span>`;
        html += `<span class="score-number ${scoreClass}">${score}/100</span>`;
        html += `</div>`;
        html += `<div class="score-bar">`;
        html += `<div class="score-progress ${scoreClass}" style="width: ${score}%"></div>`;
        html += `</div>`;
        html += `</div>`;
    }
    html += '</div>';
    html += '</div>';

    // 改进建议和总体评价并排显示
    html += '<div class="feedback-section">';

    // 改进建议
    if (evaluation.suggestions && evaluation.suggestions.length > 0) {
        html += '<div class="improvement-suggestions">';
        html += '<h4>💡 改进建议</h4>';
        html += '<ul>';
        evaluation.suggestions.forEach(suggestion => {
            html += `<li>${suggestion}</li>`;
        });
        html += '</ul>';
        html += '</div>';
    }

    // 总体评价
    if (evaluation.feedback) {
        html += '<div class="overall-feedback">';
        html += '<h4>📝 总体评价</h4>';
        html += `<p>${evaluation.feedback}</p>`;
        html += '</div>';
    }

    html += '</div>';

    // 下一步提示
    html += '<div class="next-steps">';
    html += '<h4>🚀 下一步</h4>';
    if (evaluation.passed) {
        html += '<p>🎉 恭喜您通过开场白阶段！点击"进入下一阶段"按钮开始"话天地"阶段的练习。</p>';
        html += '<p><strong>话天地阶段重点：</strong>深入了解客户需求，建立信任关系，处理客户疑虑。</p>';
    } else {
        html += '<p>⚠️ 请根据上述建议改进您的开场白话术，然后点击"重新尝试"按钮。</p>';
        html += '<p><strong>重点关注：</strong>平均分需要达到75分以上才能进入下一阶段。</p>';
    }
    html += '</div>';

    html += '</div>';

    document.getElementById('evaluation-content').innerHTML = html;
}

function toggleHistoryPanel() {
    if (historyPanel.style.display === 'none' || !historyPanel.style.display) {
        showHistoryPanel();
    } else {
        closeHistoryPanel();
    }
}

function showHistoryPanel() {
    // 生成历史记录HTML
    let html = '';

    for (const [stage, records] of Object.entries(stageHistory)) {
        html += `<div class="history-stage">`;
        html += `<h5>${stage}阶段记录</h5>`;

        records.forEach((record) => {
            html += `<div class="history-item">`;
            html += `<div class="history-header">`;
            html += `<span class="history-time">${record.timestamp}</span>`;
            html += `<span class="history-score ${record.evaluation.passed ? 'passed' : 'failed'}">`;
            html += `${record.evaluation.average_score}/100 ${record.evaluation.passed ? '✅' : '❌'}`;
            html += `</span>`;
            html += `</div>`;

            html += `<div class="history-message">`;
            html += `<strong>话术:</strong> ${record.message.substring(0, 100)}${record.message.length > 100 ? '...' : ''}`;
            html += `</div>`;

            html += `<div class="history-details">`;
            for (const [criteria, score] of Object.entries(record.evaluation.criteria_scores)) {
                const scoreClass = score >= 80 ? 'good' : score >= 60 ? 'average' : 'poor';
                html += `<span class="criteria-score ${scoreClass}">${criteria}: ${score}</span>`;
            }
            html += `</div>`;

            if (record.evaluation.suggestions && record.evaluation.suggestions.length > 0) {
                html += `<div class="history-suggestions">`;
                html += `<strong>建议:</strong> ${record.evaluation.suggestions.join('; ')}`;
                html += `</div>`;
            }

            html += `</div>`;
        });

        html += `</div>`;
    }

    if (html === '') {
        html = '<p>暂无历史记录</p>';
    }

    historyContent.innerHTML = html;
    historyPanel.style.display = 'block';
    toggleHistoryBtn.textContent = '📊 关闭历史记录';
}

function closeHistoryPanel() {
    historyPanel.style.display = 'none';
    toggleHistoryBtn.textContent = '📊 历史记录';
}

async function continueToNextStage() {
    try {
        // 如果当前是开场白阶段，直接切换到话天地阶段
        if (currentStage === '开场白') {
            currentStage = '话天地';

            // 隐藏评分面板
            evaluationPanel.style.display = 'none';

            // 更新阶段显示
            updateStageDisplay();
            updateEvaluationSection();

            // 添加系统消息
            addMessage('system', '🎯 欢迎进入话天地阶段！在这个阶段，您将与AI客户进行多轮对话，重点是深入了解客户需求，建立信任关系，处理客户疑虑。');
            addMessage('system', '💡 提示：每轮对话后，右侧会显示实时评分和改进建议。请根据客户的回应调整您的话术策略。');

            return;
        }

        // 其他阶段使用API切换
        const response = await fetch('/api/next-stage', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: currentSessionId
            })
        });

        if (!response.ok) {
            throw new Error('Failed to continue to next stage');
        }

        const data = await response.json();
        currentStage = data.current_stage;

        // 隐藏评分面板
        evaluationPanel.style.display = 'none';

        // 更新阶段显示
        updateStageDisplay();
        updateEvaluationSection();

        // 添加系统消息
        addMessage('system', data.message);

    } catch (error) {
        alert('进入下一阶段失败: ' + error.message);
    }
}

function retryCurrentStage() {
    // 隐藏评分面板
    evaluationPanel.style.display = 'none';

    // 添加系统消息
    addMessage('system', '请重新开始当前阶段的话术练习');

    // 聚焦到输入框
    chatInput.focus();
}
