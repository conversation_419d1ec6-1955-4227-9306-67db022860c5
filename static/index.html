<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>红娘话术AI陪练系统</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>红娘话术AI陪练系统</h1>
            <p>提升您的专业话术技能</p>
        </header>

        <!-- 客户信息配置面板 -->
        <div id="config-panel" class="panel">
            <h2>客户信息配置</h2>
            <form id="customer-form">
                <div class="form-group">
                    <label for="marriage-status">婚姻状态:</label>
                    <select id="marriage-status" name="婚姻状态">
                        <option value="单身">单身</option>
                        <option value="离异">离异</option>
                        <option value="丧偶">丧偶</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="marriage-intention">婚姻意愿:</label>
                    <select id="marriage-intention" name="婚姻意愿">
                        <option value="想结婚">想结婚</option>
                        <option value="不着急">不着急</option>
                        <option value="只是了解">只是了解</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="age">年龄范围:</label>
                    <select id="age" name="年龄">
                        <option value="20-25岁">20-25岁</option>
                        <option value="25-30岁">25-30岁</option>
                        <option value="30-35岁">30-35岁</option>
                        <option value="35-40岁">35-40岁</option>
                        <option value="40岁以上">40岁以上</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="gender">性别:</label>
                    <select id="gender" name="性别">
                        <option value="女">女</option>
                        <option value="男">男</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="education">学历:</label>
                    <select id="education" name="学历">
                        <option value="高中">高中</option>
                        <option value="大专">大专</option>
                        <option value="本科">本科</option>
                        <option value="硕士">硕士</option>
                        <option value="博士">博士</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="income">收入区间:</label>
                    <select id="income" name="收入区间">
                        <option value="5000以下">5000以下</option>
                        <option value="5000-8000">5000-8000</option>
                        <option value="8000-15000">8000-15000</option>
                        <option value="15000-25000">15000-25000</option>
                        <option value="25000以上">25000以上</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="location">地理位置:</label>
                    <input type="text" id="location" name="地理位置" value="北京" required>
                </div>

                <div class="form-group">
                    <label for="concerns">对婚恋公司的疑虑:</label>
                    <textarea id="concerns" name="对婚恋公司的疑虑" rows="3" placeholder="例如：担心被骗钱、怀疑服务质量等">担心被骗钱</textarea>
                </div>

                <div class="form-group">
                    <label for="requirements">对象诉求:</label>
                    <textarea id="requirements" name="对象诉求" rows="3" placeholder="例如：希望找到有稳定工作、性格温和的对象">希望找到有稳定工作、性格温和的对象</textarea>
                </div>

                <button type="submit" class="btn-primary">开始陪练</button>
            </form>
        </div>

        <!-- 对话界面 -->
        <div id="chat-panel" class="panel" style="display: none;">
            <div class="stage-info">
                <h3>当前阶段: <span id="current-stage">开场白</span></h3>
                <div class="stage-progress">
                    <div class="stage-item active" data-stage="开场白">开场白</div>
                    <div class="stage-item" data-stage="话天地">话天地</div>
                    <div class="stage-item" data-stage="人选介绍">人选介绍</div>
                    <div class="stage-item" data-stage="入主题">入主题</div>
                    <div class="stage-item" data-stage="促成">促成</div>
                </div>
            </div>

            <!-- 历史评分记录切换按钮 -->
            <button id="toggle-history-btn" class="toggle-history-btn" style="display: none;">
                📊 查看历史评分记录
            </button>

            <!-- 历史评分记录 -->
            <div id="history-panel" class="history-panel" style="display: none;">
                <h4>历史评分记录</h4>
                <div id="history-content"></div>
            </div>

            <div class="chat-container">
                <div class="chat-main">
                    <div id="chat-messages" class="chat-messages"></div>
                    
                    <div class="chat-input-container">
                        <textarea id="chat-input" placeholder="请输入您的话术..." rows="3"></textarea>
                        <button id="send-btn" class="btn-primary">发送</button>
                    </div>
                </div>
                
                <!-- 实时评分卡片 -->
                <div id="realtime-score-card" class="realtime-score-card" style="display: none;">
                    <h4 id="score-card-title">话天地阶段评分</h4>
                    <div class="score-progress">
                        <div class="progress-info">
                            <span>对话轮次: <span id="conversation-rounds">0</span>/15</span>
                            <span>平均分: <span id="average-score">0</span>/100</span>
                        </div>
                        <div class="progress-bar">
                            <div id="progress-fill" class="progress-fill"></div>
                        </div>
                    </div>
                    
                    <div id="realtime-scores" class="realtime-scores">
                        <!-- 话天地阶段评分项 -->
                        <div class="score-item" data-stage="话天地">
                            <span>需求挖掘深度</span>
                            <span class="score-value" id="score-需求挖掘深度">0/100</span>
                        </div>
                        <div class="score-item" data-stage="话天地">
                            <span>共情能力展现</span>
                            <span class="score-value" id="score-共情能力展现">0/100</span>
                        </div>
                        <div class="score-item" data-stage="话天地">
                            <span>疑虑处理效果</span>
                            <span class="score-value" id="score-疑虑处理效果">0/100</span>
                        </div>
                        <div class="score-item" data-stage="话天地">
                            <span>专业度体现</span>
                            <span class="score-value" id="score-专业度体现">0/100</span>
                        </div>
                        <div class="score-item" data-stage="话天地">
                            <span>对话节奏把控</span>
                            <span class="score-value" id="score-对话节奏把控">0/100</span>
                        </div>
                        <div class="score-item" data-stage="话天地">
                            <span>信任关系建立</span>
                            <span class="score-value" id="score-信任关系建立">0/100</span>
                        </div>
                        <div class="score-item" data-stage="话天地">
                            <span>扣题程度</span>
                            <span class="score-value" id="score-扣题程度">0/100</span>
                        </div>
                        
                        <!-- 人选介绍阶段评分项 -->
                        <div class="score-item" data-stage="人选介绍" style="display: none;">
                            <span>匹配精准度</span>
                            <span class="score-value" id="score-匹配精准度">0/100</span>
                        </div>
                        <div class="score-item" data-stage="人选介绍" style="display: none;">
                            <span>信息具体性</span>
                            <span class="score-value" id="score-信息具体性">0/100</span>
                        </div>
                        <div class="score-item" data-stage="人选介绍" style="display: none;">
                            <span>多样性展示</span>
                            <span class="score-value" id="score-多样性展示">0/100</span>
                        </div>
                        <div class="score-item" data-stage="人选介绍" style="display: none;">
                            <span>生活化描述</span>
                            <span class="score-value" id="score-生活化描述">0/100</span>
                        </div>
                        <div class="score-item" data-stage="人选介绍" style="display: none;">
                            <span>互动性保持</span>
                            <span class="score-value" id="score-互动性保持">0/100</span>
                        </div>
                        <div class="score-item" data-stage="人选介绍" style="display: none;">
                            <span>真实感营造</span>
                            <span class="score-value" id="score-真实感营造">0/100</span>
                        </div>
                        <div class="score-item" data-stage="人选介绍" style="display: none;">
                            <span>扣题程度</span>
                            <span class="score-value" id="score-扣题程度">0/100</span>
                        </div>
                    </div>
                    
                    <div id="round-score-changes" class="round-score-changes" style="display: none;">
                        <h5>本轮得分变化</h5>
                        <div id="score-changes-content"></div>
                    </div>
                    
                    <div id="latest-feedback" class="latest-feedback">
                        <h5>最新反馈</h5>
                        <div id="feedback-content">开始对话后将显示评分反馈</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 评分结果面板 -->
        <div id="evaluation-panel" class="panel" style="display: none;">
            <h3>评分结果</h3>
            <div id="evaluation-content"></div>
            <div class="evaluation-actions">
                <button id="continue-btn" class="btn-primary" style="display: none;">继续下一阶段</button>
                <button id="retry-btn" class="btn-secondary">重新尝试</button>
            </div>
        </div>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
