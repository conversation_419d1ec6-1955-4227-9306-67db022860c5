/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2em;
    opacity: 0.9;
}

/* 面板样式 */
.panel {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 按钮样式 */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-left: 10px;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* 阶段进度条 */
.stage-info {
    margin-bottom: 20px;
}

.stage-info h3 {
    margin-bottom: 15px;
    color: #333;
}

.stage-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.stage-item {
    flex: 1;
    text-align: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 0 5px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.stage-item.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: scale(1.05);
}

.stage-item.completed {
    background: #28a745;
    color: white;
}

/* 聊天界面样式 */
.chat-container {
    height: 500px;
    display: flex;
    gap: 20px;
}

.chat-main {
    flex: 2;
    display: flex;
    flex-direction: column;
}

/* 实时评分卡片样式 */
.realtime-score-card {
    flex: 1;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.realtime-score-card h4 {
    margin-bottom: 15px;
    color: #495057;
    text-align: center;
    font-size: 1.1em;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 10px;
}

.score-progress {
    margin-bottom: 20px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 0.9em;
    color: #6c757d;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.3s ease;
}

.realtime-scores {
    margin-bottom: 20px;
}

.realtime-scores .score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
    font-size: 0.9em;
}

.realtime-scores .score-item:last-child {
    border-bottom: none;
}

.round-score-changes {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.round-score-changes h5 {
    margin-bottom: 10px;
    color: #856404;
    font-size: 1em;
}

.score-change-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    font-size: 0.9em;
}

.score-change-positive {
    color: #28a745;
    font-weight: bold;
}

.score-change-negative {
    color: #dc3545;
    font-weight: bold;
}

.score-change-neutral {
    color: #6c757d;
}

.latest-feedback {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.latest-feedback h5 {
    margin-bottom: 10px;
    color: #495057;
    font-size: 1em;
}

#feedback-content {
    font-size: 0.9em;
    line-height: 1.4;
    color: #6c757d;
}

.feedback-suggestion {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 10px;
    margin: 8px 0;
    border-radius: 4px;
    font-size: 0.85em;
}

.stage-complete-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    font-weight: bold;
    margin: 10px 0;
}

.stage-failed-message {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    font-weight: bold;
    margin: 10px 0;
}

.retry-button {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 10px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.retry-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 20px;
}

/* 消息容器样式 */
.message-container {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
    animation: messageSlide 0.3s ease-out;
}

.message-container.user {
    flex-direction: row-reverse;
    justify-content: flex-start;
}

.message-container.assistant {
    flex-direction: row;
    justify-content: flex-start;
}

.message-container.system {
    justify-content: center;
}

/* 头像和名字样式 */
.message-avatar {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 60px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-bottom: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.speaker-name {
    font-size: 12px;
    color: #666;
    text-align: center;
    font-weight: bold;
}

/* 消息气泡样式 */
.message {
    padding: 12px 18px;
    border-radius: 18px;
    max-width: 300px;
    word-wrap: break-word;
    position: relative;
}

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: left;
}

.message.assistant {
    background: white;
    border: 2px solid #e1e5e9;
    text-align: left;
}

.message.system {
    background: #fff3cd;
    border: 2px solid #ffeaa7;
    text-align: center;
    font-style: italic;
    max-width: 90%;
    margin: 0 auto;
}

/* 用户消息的头像样式 */
.message-container.user .avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* 助手消息的头像样式 */
.message-container.assistant .avatar {
    background: #e9ecef;
    color: #495057;
}

.chat-input-container {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

#chat-input {
    flex: 1;
    resize: vertical;
    min-height: 60px;
}

#send-btn {
    height: 60px;
    white-space: nowrap;
}

/* 评分结果样式 */
.evaluation-content {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e1e5e9;
}

.score-item:last-child {
    border-bottom: none;
}

.score-value {
    font-weight: bold;
    font-size: 1.1em;
}

.score-value.good {
    color: #28a745;
}

.score-value.average {
    color: #ffc107;
}

.score-value.poor {
    color: #dc3545;
}

.average-score {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    font-size: 1.2em;
    font-weight: bold;
    margin: 15px 0;
}

.suggestions {
    margin-top: 20px;
}

.suggestions h4 {
    margin-bottom: 10px;
    color: #333;
}

.suggestions ul {
    padding-left: 20px;
}

.suggestions li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.evaluation-actions {
    text-align: center;
}

/* 历史评分记录样式 */
.history-panel {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.history-panel h4 {
    margin-bottom: 15px;
    color: #495057;
    font-size: 1.1em;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 8px;
}

.history-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.history-item:last-child {
    margin-bottom: 0;
}

.history-stage {
    font-weight: bold;
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1.05em;
}

.history-scores {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 10px;
}

.history-score-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    font-size: 0.9em;
}

.history-average {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    text-align: center;
    font-weight: bold;
    margin: 10px 0;
    font-size: 0.95em;
}

.history-suggestions {
    margin-top: 10px;
}

.history-suggestions h5 {
    margin-bottom: 8px;
    color: #495057;
    font-size: 0.95em;
}

.history-suggestions ul {
    padding-left: 15px;
    margin: 0;
}

.history-suggestions li {
    margin-bottom: 5px;
    font-size: 0.9em;
    line-height: 1.4;
}

.toggle-history-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    margin-bottom: 15px;
    transition: background-color 0.3s ease;
}

.toggle-history-btn:hover {
    background: #5a6268;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .panel {
        padding: 20px;
    }
    
    .stage-progress {
        flex-direction: column;
    }
    
    .stage-item {
        margin: 5px 0;
    }
    
    .chat-container {
        flex-direction: column;
        height: auto;
    }
    
    .chat-main {
        height: 400px;
    }
    
    .realtime-score-card {
        max-height: 300px;
        margin-top: 20px;
    }
    
    .chat-input-container {
        flex-direction: column;
    }
    
    #send-btn {
        height: auto;
        padding: 12px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 评分加载状态样式 */
.evaluation-loading {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner {
    margin-bottom: 20px;
}

.evaluation-loading p {
    color: #6c757d;
    font-size: 1.1em;
    margin: 0;
}
