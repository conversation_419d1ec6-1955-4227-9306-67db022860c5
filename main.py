from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import StreamingResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import json
import uuid
from typing import Dict
from dotenv import load_dotenv

from models import (
    SessionData, CustomerProfile, ChatRequest,
    Stage, ChatMessage
)
from llm_service import LLMService

# 加载环境变量
load_dotenv()

app = FastAPI(title="红娘话术AI陪练系统")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 全局变量存储会话数据（生产环境应使用数据库）
sessions: Dict[str, SessionData] = {}

# 初始化LLM服务
llm_service = LLMService()

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回主页面"""
    try:
        with open("static/index.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="<h1>页面未找到</h1><p>请确保static/index.html文件存在</p>")

@app.post("/api/start-session")
async def start_session(customer_profile: CustomerProfile):
    """开始新的陪练会话"""
    session_id = str(uuid.uuid4())

    session_data = SessionData(
        session_id=session_id,
        customer_profile=customer_profile,
        current_stage=Stage.OPENING,
        chat_history=[],
        stage_scores={},
        stage_attempts={stage.value: 0 for stage in Stage},
        is_stage_passed={stage.value: False for stage in Stage}
    )

    sessions[session_id] = session_data

    return {
        "session_id": session_id,
        "current_stage": session_data.current_stage,
        "customer_profile": customer_profile.model_dump(),
        "message": "会话已创建，请开始您的开场白"
    }

@app.post("/api/chat")
async def chat(chat_request: ChatRequest):
    """处理聊天请求"""
    if chat_request.session_id not in sessions:
        raise HTTPException(status_code=404, detail="会话不存在")

    session = sessions[chat_request.session_id]

    # 检查阶段是否匹配
    if chat_request.stage != session.current_stage:
        raise HTTPException(status_code=400, detail="阶段不匹配")

    # 添加用户消息到历史
    user_message = ChatMessage(role="user", content=chat_request.message)
    session.chat_history.append(user_message)

    # 如果是开场白阶段，直接评分并显示结果
    if session.current_stage == Stage.OPENING:
        return await handle_opening_stage(session, chat_request.message)
    else:
        return await handle_conversation_stage(session, chat_request.message)

async def handle_opening_stage(session: SessionData, message: str):
    """处理开场白阶段"""
    # 增加尝试次数
    session.stage_attempts[Stage.OPENING.value] += 1

    # 评估开场白
    evaluation = await llm_service.evaluate_matchmaker_speech(
        message, session.customer_profile, session.current_stage
    )

    # 保存评分
    session.stage_scores[Stage.OPENING.value] = evaluation

    # 检查是否通过
    if evaluation["passed"]:
        session.is_stage_passed[Stage.OPENING.value] = True
        session.current_stage = Stage.CHAT

        return {
            "evaluation": evaluation,
            "stage_completed": True,
            "next_stage": Stage.CHAT.value,
            "message": "恭喜！开场白通过，进入下一阶段：话天地",
            "attempts": session.stage_attempts[Stage.OPENING.value]
        }
    else:
        return {
            "evaluation": evaluation,
            "stage_completed": False,
            "next_stage": None,
            "message": f"开场白未通过，请重新尝试。当前尝试次数：{session.stage_attempts[Stage.OPENING.value]}",
            "attempts": session.stage_attempts[Stage.OPENING.value]
        }

async def handle_conversation_stage(session: SessionData, message: str):
    """处理对话阶段（非开场白）"""

    async def generate_response():
        response_content = ""
        async for chunk in llm_service.get_customer_response(
            message,
            session.customer_profile,
            session.current_stage,
            [msg.model_dump() for msg in session.chat_history[-10:]]  # 最近10条消息
        ):
            response_content += chunk
            yield f"data: {json.dumps({'type': 'content', 'data': chunk}, ensure_ascii=False)}\n\n"

        # 添加AI回复到历史
        ai_message = ChatMessage(role="assistant", content=response_content)
        session.chat_history.append(ai_message)

        # 发送完成信号
        yield f"data: {json.dumps({'type': 'complete', 'data': response_content}, ensure_ascii=False)}\n\n"

        # 发送评分信号（用于实时评分）
        yield f"data: {json.dumps({'type': 'evaluation_trigger', 'message': message}, ensure_ascii=False)}\n\n"

    return StreamingResponse(
        generate_response(),
        media_type="text/plain",
        headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
    )

@app.post("/api/customer-response")
async def get_customer_response_api(request: dict):
    """获取客户回复API"""
    session_id = request.get("session_id")
    message = request.get("message")
    stage = request.get("stage")

    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="会话不存在")

    session = sessions[session_id]

    try:
        # 获取客户回复
        response_content = ""
        async for chunk in llm_service.get_customer_response(
            message,
            session.customer_profile,
            Stage(stage) if stage else session.current_stage,
            [msg.model_dump() for msg in session.chat_history[-5:]]
        ):
            response_content += chunk

        # 添加到聊天历史
        user_message = ChatMessage(role="user", content=message)
        ai_message = ChatMessage(role="assistant", content=response_content)
        session.chat_history.extend([user_message, ai_message])

        return {"response": response_content}

    except Exception as e:
        return {"response": f"客户回复获取失败: {str(e)}"}

@app.post("/api/evaluate-realtime")
async def evaluate_realtime(request: dict):
    """实时评分API（用于话天地等阶段）"""
    session_id = request.get("session_id")
    message = request.get("message")

    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="会话不存在")

    session = sessions[session_id]

    # 只对非开场白阶段进行实时评分
    if session.current_stage == Stage.OPENING:
        return {"error": "开场白阶段不支持实时评分"}

    try:
        # 评估当前话术
        evaluation = await llm_service.evaluate_matchmaker_speech(
            message, session.customer_profile, session.current_stage
        )

        # 保存评分到会话历史
        if session.current_stage.value not in session.stage_scores:
            session.stage_scores[session.current_stage.value] = []

        session.stage_scores[session.current_stage.value].append({
            "message": message,
            "evaluation": evaluation,
            "timestamp": str(uuid.uuid4())[:8]  # 简短的时间戳
        })

        return {
            "evaluation": evaluation,
            "round_number": len(session.stage_scores[session.current_stage.value]),
            "stage": session.current_stage.value
        }

    except Exception as e:
        return {"error": str(e)}

@app.post("/api/next-stage")
async def next_stage(request: dict):
    """进入下一阶段"""
    session_id = request.get("session_id")
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="会话不存在")

    session = sessions[session_id]

    # 开场白阶段不允许手动跳转，必须通过评分
    if session.current_stage == Stage.OPENING:
        return {
            "current_stage": session.current_stage.value,
            "message": "开场白阶段需要通过评分才能进入下一阶段"
        }

    # 获取下一阶段
    stages = list(Stage)
    current_index = stages.index(session.current_stage)

    if current_index < len(stages) - 1:
        session.current_stage = stages[current_index + 1]
        return {
            "current_stage": session.current_stage.value,
            "message": f"已进入阶段：{session.current_stage.value}"
        }
    else:
        return {
            "current_stage": session.current_stage.value,
            "message": "已完成所有阶段"
        }

@app.get("/api/session/{session_id}")
async def get_session(session_id: str):
    """获取会话信息"""
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="会话不存在")

    session = sessions[session_id]
    return {
        "session_id": session_id,
        "current_stage": session.current_stage.value,
        "customer_profile": session.customer_profile.model_dump(),
        "chat_history": [msg.model_dump() for msg in session.chat_history],
        "stage_scores": session.stage_scores,
        "stage_attempts": session.stage_attempts,
        "is_stage_passed": session.is_stage_passed
    }

@app.post("/api/evaluate-chat-stage")
async def evaluate_chat_stage(request: dict):
    """评估话天地阶段的消息"""
    session_id = request.get("session_id")
    message = request.get("message")
    conversation_rounds = request.get("conversation_rounds", 0)

    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="会话不存在")

    session = sessions[session_id]

    # 只在话天地阶段进行评分
    if session.current_stage != Stage.CHAT:
        raise HTTPException(status_code=400, detail="当前阶段不支持此评分")

    try:
        # 调用LLM服务进行话天地阶段评分
        evaluation = await llm_service.evaluate_chat_stage_message(
            message,
            session.customer_profile,
            conversation_rounds,
            [msg.model_dump() for msg in session.chat_history[-20:]]  # 最近5条消息作为上下文
        )

        return evaluation

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"评分失败: {str(e)}")

@app.post("/api/evaluate-candidate-intro")
async def evaluate_candidate_intro(request: dict):
    """评估人选介绍阶段的消息"""
    session_id = request.get("session_id")
    message = request.get("message")
    conversation_rounds = request.get("conversation_rounds", 0)

    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="会话不存在")

    session = sessions[session_id]

    # 只在人选介绍阶段进行评分
    if session.current_stage != Stage.INTRODUCTION:
        raise HTTPException(status_code=400, detail="当前阶段不支持此评分")

    try:
        # 调用LLM服务进行人选介绍阶段评分
        evaluation = await llm_service.evaluate_candidate_intro_message(
            message,
            session.customer_profile,
            conversation_rounds,
            [msg.model_dump() for msg in session.chat_history[-20:]]  # 最近20条消息作为上下文
        )

        return evaluation

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"评分失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
