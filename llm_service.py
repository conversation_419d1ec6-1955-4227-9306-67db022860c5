import os
import asyncio
from openai import Async<PERSON><PERSON>A<PERSON>
from typing import AsyncGenerator, Dict, Any
from models import CustomerProfile, Stage
from prompts import STAGE_CUSTOMER_PROMPTS, EVALUATION_PROMPTS
import json
import re



class LLMService:
    def __init__(self):
        self.client = AsyncOpenAI(
            api_key=os.environ.get('huoshan_prod_key'),
            base_url=os.environ.get('BASE_URL', "https://ark.cn-beijing.volces.com/api/v3/")
        )
        self.model_id = os.environ.get('MODEL_ID', "doubao-1-5-pro-32k-250115")

    async def get_customer_response(
        self,
        matchmaker_message: str,
        customer_profile: CustomerProfile,
        stage: Stage,
        chat_history: list = None
    ) -> AsyncGenerator[str, None]:
        """获取客户AI的回应（流式输出）"""

        # 构建prompt
        prompt = STAGE_CUSTOMER_PROMPTS[stage.value].format(**customer_profile.model_dump())

        # 构建消息历史
        messages = [
            {"role": "system", "content": prompt}
        ]

        # 添加聊天历史
        if chat_history:
            for msg in chat_history[-20:]:  # 只保留最近6条消息
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })

        # 添加当前红娘消息
        messages.append({
            "role": "user",
            "content": f"红娘说：{matchmaker_message}\n\n请作为客户回应："
        })

        try:
            response = await self.client.chat.completions.create(
                model=self.model_id,
                messages=messages,
                stream=True,
                temperature=0.7,
                max_tokens=500
            )

            async for chunk in response:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content

        except Exception as e:
            yield f"Error: {str(e)}"

    async def evaluate_matchmaker_speech(
        self,
        matchmaker_message: str,
        customer_profile: CustomerProfile,
        stage: Stage = Stage.OPENING
    ) -> Dict[str, Any]:
        """评估红娘话术"""

        # 根据阶段选择对应的评分提示词
        evaluation_prompt = EVALUATION_PROMPTS.get(stage.value, EVALUATION_PROMPTS["开场白"])

        prompt = evaluation_prompt.format(
            matchmaker_message=matchmaker_message,
            customer_profile=customer_profile.model_dump()
        )

        messages = [
            {"role": "system", "content": "你是一位专业的红娘话术评估专家。"},
            {"role": "user", "content": prompt}
        ]

        try:
            response = await self.client.chat.completions.create(
                model=self.model_id,
                messages=messages,
                temperature=0.3,
                max_tokens=1000
            )

            evaluation_text = response.choices[0].message.content
            return self._parse_evaluation(evaluation_text, stage)

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            return {
                "error": str(e),
                "criteria_scores": {
                    "自我介绍明确性": 0,
                    "来电目的说明": 0,
                    "获号渠道说明": 0,
                    "匹配点提及": 0,
                    "避免强推话术": 0,
                    "语气自然度": 0
                },
                "average_score": 0,
                "passed": False,
                "feedback": "评估出错",
                "suggestions": []
            }

    def _parse_evaluation(self, evaluation_text: str, stage: Stage = Stage.OPENING) -> Dict[str, Any]:
        """解析评估结果"""
        try:
            # 根据阶段定义评分标准
            stage_criteria = {
                "开场白": ["自我介绍明确性", "来电目的说明", "获号渠道说明", "匹配点提及", "避免强推话术", "语气自然度"],
                "话天地": ["需求挖掘深度", "共情能力展现", "疑虑处理效果", "专业度体现", "对话节奏把控", "信任关系建立"],
                "人选介绍": ["匹配精准度", "信息具体性", "多样性展示", "生活化描述", "互动性保持", "真实感营造"],
                "入主题": ["安全感营造", "流程说明清晰", "必要性阐述", "身份验证强调", "顾虑消除效果", "引导自然度"],
                "促成": ["时机把握", "细节确认", "诚意表达", "压力控制", "决策引导", "专业收尾"]
            }

            # 提取各项分数
            criteria_scores = {}
            criteria_names = stage_criteria.get(stage.value, stage_criteria["开场白"])

            for criteria in criteria_names:
                pattern = rf"{criteria}:\s*(\d+)/100"
                match = re.search(pattern, evaluation_text)
                if match:
                    criteria_scores[criteria] = float(match.group(1))
                else:
                    criteria_scores[criteria] = 0

            # 计算平均分
            average_score = sum(criteria_scores.values()) / len(criteria_scores)

            # 判断是否通过
            passed = average_score > 75

            # 提取改进建议
            suggestions = []
            suggestion_section = re.search(r"改进建议：\s*(.*?)(?=总体评价：|$)", evaluation_text, re.DOTALL)
            if suggestion_section:
                suggestion_lines = suggestion_section.group(1).strip().split('\n')
                for line in suggestion_lines:
                    line = line.strip()
                    if line and (line.startswith('1.') or line.startswith('2.') or line.startswith('3.') or line.startswith('-')):
                        suggestions.append(line)

            # 提取总体评价
            feedback_section = re.search(r"总体评价：\s*(.*?)$", evaluation_text, re.DOTALL)
            feedback = feedback_section.group(1).strip() if feedback_section else "无评价"

            return {
                "criteria_scores": criteria_scores,
                "average_score": round(average_score, 1),
                "passed": passed,
                "feedback": feedback,
                "suggestions": suggestions,
                "raw_evaluation": evaluation_text
            }

        except Exception as e:
            return {
                "error": f"解析评估结果失败: {str(e)}",
                "criteria_scores": {name: 0 for name in criteria_names},
                "average_score": 0,
                "passed": False,
                "feedback": "解析失败",
                "suggestions": [],
                "raw_evaluation": evaluation_text
            }

    async def evaluate_chat_stage_message(
        self,
        matchmaker_message: str,
        customer_profile: CustomerProfile,
        conversation_rounds: int,
        chat_history: list = None
    ) -> Dict[str, Any]:
        """评估话天地阶段的红娘话术"""
        chat_history = chat_history[:-1]
        print('=====================================')
        print('chat_history:')
        print(chat_history)
        print('matchmaker_message')
        print(matchmaker_message)
        print('======================================')
        # 构建话天地阶段评估prompt
        chat_stage_prompt = f"""
你是一位专业的红娘话术评估专家，专门评估"话天地"阶段的对话质量。

客户信息：
{customer_profile.model_dump()}

当前对话轮次：{conversation_rounds}/15

聊天历史：
{self._format_chat_history(chat_history) if chat_history else "无历史记录"}

红娘当前话术：
{matchmaker_message}

请根据以下7个标准对红娘本轮话术进行评分变化（每项可以是正分、负分或0分，范围-20到+20）：

1. 需求挖掘深度：本轮是否有助于深入了解客户需求（如果没有涉及则为0分）
2. 共情能力展现：本轮是否展现了共情能力（如果没有涉及则为0分，表现好则加分，表现差则减分）
3. 疑虑处理效果：本轮是否有效处理了客户疑虑（如果没有涉及则为0分）
4. 专业度体现：本轮是否展现了专业度（如果没有涉及则为0分）
5. 对话节奏把控：本轮节奏把控如何（如果没有涉及则为0分）
6. 信任关系建立：本轮是否有助于建立信任（如果没有涉及则为0分）
7. 扣题程度：红娘本轮回复是否恰到好处地回应了客户前一句话（如果跑题则扣分，如果扣题则加分，扣分和加分情况视扣题、跑题程度而定）

注意：
- 如果某个方面在本轮对话中没有涉及到，则该项得分为0
- 如果涉及到某个方面且表现良好，则给正分（+1到+20）
- 如果涉及到某个方面但表现不佳，则给负分（-1到-20）
- 评分要客观公正，不要过于宽松或严格

请按以下格式输出本轮得分变化：

需求挖掘深度: +XX 或 -XX 或 0
共情能力展现: +XX 或 -XX 或 0
疑虑处理效果: +XX 或 -XX 或 0
专业度体现: +XX 或 -XX 或 0
对话节奏把控: +XX 或 -XX 或 0
信任关系建立: +XX 或 -XX 或 0
扣题程度: +XX 或 -XX 或 0

针对性反馈：
[针对这句话术的具体改进建议，不超过100字]
"""

        messages = [
            {"role": "system", "content": "你是一位专业的红娘话术评估专家，专门评估话天地阶段的对话质量。"},
            {"role": "user", "content": chat_stage_prompt}
        ]

        try:
            response = await self.client.chat.completions.create(
                model=self.model_id,
                messages=messages,
                temperature=0.3,
                max_tokens=800
            )

            evaluation_text = response.choices[0].message.content
            return self._parse_chat_stage_evaluation(evaluation_text)

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            return {
                "error": str(e),
                "score_changes": {
                    "需求挖掘深度": 0,
                    "共情能力展现": 0,
                    "疑虑处理效果": 0,
                    "专业度体现": 0,
                    "对话节奏把控": 0,
                    "信任关系建立": 0,
                    "扣题程度": 0
                },
                "feedback": "评估出错"
            }

    def _format_chat_history(self, chat_history: list) -> str:
        """格式化聊天历史"""
        if not chat_history:
            return "无历史记录"

        formatted = []
        for msg in chat_history:
            role = "红娘" if msg["role"] == "user" else "客户"
            formatted.append(f"{role}: {msg['content']}")

        return "\n".join(formatted)

    def _parse_chat_stage_evaluation(self, evaluation_text: str) -> Dict[str, Any]:
        """解析话天地阶段评估结果"""
        try:
            # 提取各项得分变化
            score_changes = {}
            criteria_names = ["需求挖掘深度", "共情能力展现", "疑虑处理效果", "专业度体现", "对话节奏把控", "信任关系建立", "扣题程度"]

            for criteria in criteria_names:
                # 匹配 +XX, -XX, 或 0 的格式
                pattern = rf"{criteria}:\s*([+-]?\d+)"
                match = re.search(pattern, evaluation_text)
                if match:
                    score_changes[criteria] = int(match.group(1))
                else:
                    score_changes[criteria] = 0

            # 提取针对性反馈
            feedback_section = re.search(r"针对性反馈：\s*(.*?)$", evaluation_text, re.DOTALL)
            feedback = feedback_section.group(1).strip() if feedback_section else "无反馈"

            return {
                "score_changes": score_changes,
                "feedback": feedback,
                "raw_evaluation": evaluation_text
            }

        except Exception as e:
            return {
                "error": f"解析评估结果失败: {str(e)}",
                "score_changes": {name: 0 for name in criteria_names},
                "feedback": "解析失败",
                "raw_evaluation": evaluation_text
            }

    async def evaluate_candidate_intro_message(
        self,
        matchmaker_message: str,
        customer_profile: CustomerProfile,
        conversation_rounds: int,
        chat_history: list = None
    ) -> Dict[str, Any]:
        """评估人选介绍阶段的红娘话术"""
        chat_history = chat_history[:-1] if chat_history else []

        # 构建人选介绍阶段评估prompt
        candidate_intro_prompt = f"""
你是一位专业的红娘话术评估专家，专门评估"人选介绍"阶段的对话质量。

客户信息：
{customer_profile.model_dump()}

当前对话轮次：{conversation_rounds}/15

聊天历史：
{self._format_chat_history(chat_history) if chat_history else "无历史记录"}

红娘当前话术：
{matchmaker_message}

请根据以下7个标准对红娘本轮话术进行评分变化（每项可以是正分、负分或0分，范围-20到+20）：

1. 匹配精准度：介绍的候选人是否符合客户的择偶要求（如果没有涉及则为0分）
2. 信息具体性：是否提供具体信息（职业、年龄、爱好等）而非模糊描述（如果没有涉及则为0分）
3. 多样性展示：是否介绍多个候选人，展示选择空间（如果没有涉及则为0分）
4. 生活化描述：是否描绘可能的相处场景，而非干巴巴列举条件（如果没有涉及则为0分）
5. 互动性保持：是否询问客户意见，根据反馈调整介绍方向（如果没有涉及则为0分）
6. 真实感营造：是否让客户相信这些是真实存在的候选人（如果没有涉及则为0分）
7. 扣题程度：红娘本轮回复是否恰到好处地回应了客户前一句话（如果跑题则扣分，如果扣题则加分，扣分和加分情况视扣题、跑题程度而定）

注意：
- 如果某个方面在本轮对话中没有涉及到，则该项得分为0
- 如果涉及到某个方面且表现良好，则给正分（+1到+20）
- 如果涉及到某个方面但表现不佳，则给负分（-1到-20）
- 评分要客观公正，不要过于宽松或严格

请按以下格式输出本轮得分变化：

匹配精准度: +XX 或 -XX 或 0
信息具体性: +XX 或 -XX 或 0
多样性展示: +XX 或 -XX 或 0
生活化描述: +XX 或 -XX 或 0
互动性保持: +XX 或 -XX 或 0
真实感营造: +XX 或 -XX 或 0
扣题程度: +XX 或 -XX 或 0

针对性反馈：
[针对这句话术的具体改进建议，不超过100字]
"""

        messages = [
            {"role": "system", "content": "你是一位专业的红娘话术评估专家，专门评估人选介绍阶段的对话质量。"},
            {"role": "user", "content": candidate_intro_prompt}
        ]

        try:
            response = await self.client.chat.completions.create(
                model=self.model_id,
                messages=messages,
                temperature=0.3,
                max_tokens=800
            )

            evaluation_text = response.choices[0].message.content
            return self._parse_candidate_intro_evaluation(evaluation_text)

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            return {
                "error": str(e),
                "score_changes": {
                    "匹配精准度": 0,
                    "信息具体性": 0,
                    "多样性展示": 0,
                    "生活化描述": 0,
                    "互动性保持": 0,
                    "真实感营造": 0,
                    "扣题程度": 0
                },
                "feedback": "评估出错"
            }

    def _parse_candidate_intro_evaluation(self, evaluation_text: str) -> Dict[str, Any]:
        """解析人选介绍阶段评估结果"""
        try:
            # 提取各项得分变化
            score_changes = {}
            criteria_names = ["匹配精准度", "信息具体性", "多样性展示", "生活化描述", "互动性保持", "真实感营造", "扣题程度"]

            for criteria in criteria_names:
                # 匹配 +XX, -XX, 或 0 的格式
                pattern = rf"{criteria}:\s*([+-]?\d+)"
                match = re.search(pattern, evaluation_text)
                if match:
                    score_changes[criteria] = int(match.group(1))
                else:
                    score_changes[criteria] = 0

            # 提取针对性反馈
            feedback_section = re.search(r"针对性反馈：\s*(.*?)$", evaluation_text, re.DOTALL)
            feedback = feedback_section.group(1).strip() if feedback_section else "无反馈"

            return {
                "score_changes": score_changes,
                "feedback": feedback,
                "raw_evaluation": evaluation_text
            }

        except Exception as e:
            return {
                "error": f"解析评估结果失败: {str(e)}",
                "score_changes": {name: 0 for name in criteria_names},
                "feedback": "解析失败",
                "raw_evaluation": evaluation_text
            }
