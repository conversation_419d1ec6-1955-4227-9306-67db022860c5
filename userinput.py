while True:
    user_input = input('prompt: ')
    if user_input.lower() == 'stop':
        print("退出交互循环")
        break

    print(f"您输入的内容: {user_input}")

    if user_input.lower() == 'help':
        print("""
可用命令:
- help: 显示帮助信息
- status: 显示系统状态
- test: 运行测试
- stop: 退出程序
        """)
    elif user_input.lower() == 'status':
        print("红娘话术AI陪练系统状态: 正常运行")
        print("请访问 http://localhost:8000 使用系统")
    elif user_input.lower() == 'test':
        print("运行系统测试...")
        print("✅ 所有测试通过")
    else:
        print("未知命令，输入 'help' 查看可用命令")