# 红娘话术AI陪练系统

一个基于LLM的红娘话术训练系统，帮助红娘提升专业话术技能。系统包含对话AI（扮演潜在客户）和评分AI（评判话术优劣）。

## 功能特点

- **多阶段训练**：开场白、话天地、人选介绍、入主题、促成五个阶段
- **智能评分**：6个维度的专业评分标准
- **实时对话**：流式输出，自然对话体验
- **个性化客户**：可配置不同类型的客户画像
- **进度跟踪**：记录练习次数和评分历史

## 技术架构

- **后端**：Python FastAPI + AsyncOpenAI
- **前端**：HTML + CSS + JavaScript
- **LLM**：豆包模型（doubao-1-5-pro-32k-250115）
- **特性**：流式输出、异步处理

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

编辑 `.env` 文件，设置您的豆包API密钥：

```env
HUOSHAN_PROD_KEY=your_api_key_here
BASE_URL=https://ark.cn-beijing.volces.com/api/v3/
MODEL_ID=doubao-1-5-pro-32k-250115
```

### 3. 启动服务

```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 4. 访问系统

打开浏览器访问：http://localhost:8000

## 使用说明

### 1. 配置客户信息
- 设置客户的基本信息（年龄、性别、学历等）
- 配置客户的疑虑和对象诉求
- 点击"开始陪练"创建会话

### 2. 开场白阶段
- 输入您的开场白话术
- 系统会根据6个标准进行评分：
  - 自我介绍明确性
  - 来电目的说明
  - 获号渠道说明
  - 匹配点提及
  - 避免强推话术
  - 语气自然度
- 平均分>75分才能进入下一阶段

### 3. 后续阶段
- 与AI客户进行多轮对话
- 每轮对话都会收到实时反馈
- 根据客户反应调整话术策略

## 评分标准

### 开场白阶段评分标准
1. **自我介绍明确性**：是否清楚说明"我是红娘/婚恋顾问"的身份
2. **来电目的说明**：是否解释是为了介绍对象、提供婚恋服务
3. **获号渠道说明**：是否合理解释如何获得客户联系方式
4. **匹配点提及**：是否提到客户与候选人的某些共同点
5. **避免强推话术**：是否避免"马上见面""立刻决定"等压迫性语言
6. **语气自然度**：是否像正常对话而非念稿，有停顿和互动

## 项目结构

```
├── main.py              # FastAPI主应用
├── models.py            # 数据模型定义
├── prompts.py           # LLM提示词模板
├── llm_service.py       # LLM服务封装
├── requirements.txt     # Python依赖
├── .env                 # 环境变量配置
└── static/              # 前端静态文件
    ├── index.html       # 主页面
    ├── style.css        # 样式文件
    └── script.js        # JavaScript逻辑
```

## API接口

- `POST /api/start-session` - 开始新的陪练会话
- `POST /api/chat` - 发送聊天消息
- `POST /api/next-stage` - 进入下一阶段
- `GET /api/session/{session_id}` - 获取会话信息

## 开发说明

### 添加新的评分标准
1. 在 `models.py` 中更新 `EvaluationCriteria` 模型
2. 在 `prompts.py` 中修改 `EVALUATION_PROMPT` 模板
3. 在 `llm_service.py` 中更新解析逻辑

### 添加新的对话阶段
1. 在 `models.py` 中的 `Stage` 枚举添加新阶段
2. 在 `prompts.py` 中添加对应的客户提示词
3. 更新前端的阶段进度显示

## 注意事项

- 确保豆包API密钥有效且有足够的配额
- 系统目前使用内存存储会话数据，重启后会丢失
- 生产环境建议使用Redis或数据库存储会话数据
- 建议配置HTTPS以保护API密钥安全